digraph {
	graph [size="378.75,378.75"]
	node [align=left fontname=monospace fontsize=10 height=0.2 ranksep=0.1 shape=box style=filled]
	2186474662624 [label="
 ()" fillcolor=darkolivegreen1]
	2186917551024 [label=AddBackward0]
	2186917548864 -> 2186917551024
	2186917548864 [label=DivBackward0]
	2186917549008 -> 2186917548864
	2186917549008 [label=SumBackward0]
	2186726486512 -> 2186917549008
	2186726486512 [label=MulBackward0]
	2186917550448 -> 2186726486512
	2186917550448 [label=NllLossBackward0]
	2186474789376 -> 2186917550448
	2186474789376 [label=LogSoftmaxBackward0]
	2186474790816 -> 2186474789376
	2186474790816 [label=ViewBackward0]
	2186474680176 -> 2186474790816
	2186474680176 [label=ViewBackward0]
	2186474797232 -> 2186474680176
	2186474797232 [label=AddmmBackward0]
	2186474527424 -> 2186474797232
	2184990842288 [label="cls.predictions.bias
 (23236)" fillcolor=lightblue]
	2184990842288 -> 2186474527424
	2186474527424 [label=AccumulateGrad]
	2186996746416 -> 2186474797232
	2186996746416 [label=ViewBackward0]
	2186474768128 -> 2186996746416
	2186474768128 [label=NativeLayerNormBackward0]
	2186474770192 -> 2186474768128
	2186474770192 [label=GeluBackward0]
	2186474770240 -> 2186474770192
	2186474770240 [label=ViewBackward0]
	2186474768080 -> 2186474770240
	2186474768080 [label=AddmmBackward0]
	2186474528096 -> 2186474768080
	2184990842528 [label="cls.predictions.transform.dense.bias
 (768)" fillcolor=lightblue]
	2184990842528 -> 2186474528096
	2186474528096 [label=AccumulateGrad]
	2186474768704 -> 2186474768080
	2186474768704 [label=ViewBackward0]
	2186474767744 -> 2186474768704
	2186474767744 [label=NativeLayerNormBackward0]
	2186474770336 -> 2186474767744
	2186474770336 [label=AddBackward0]
	2186474767936 -> 2186474770336
	2186474767936 [label=NativeDropoutBackward0]
	2186726657616 -> 2186474767936
	2186726657616 [label=ViewBackward0]
	2186726657808 -> 2186726657616
	2186726657808 [label=AddmmBackward0]
	2186474619088 -> 2186726657808
	2184990639904 [label="bert.encoder.layer.11.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990639904 -> 2186474619088
	2186474619088 [label=AccumulateGrad]
	2186726657280 -> 2186726657808
	2186726657280 [label=ViewBackward0]
	2186726657136 -> 2186726657280
	2186726657136 [label=GeluBackward0]
	2186726656848 -> 2186726657136
	2186726656848 [label=ViewBackward0]
	2186726656416 -> 2186726656848
	2186726656416 [label=AddmmBackward0]
	2186474619712 -> 2186726656416
	2184990640384 [label="bert.encoder.layer.11.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184990640384 -> 2186474619712
	2186474619712 [label=AccumulateGrad]
	2186726656080 -> 2186726656416
	2186726656080 [label=ViewBackward0]
	2186474767264 -> 2186726656080
	2186474767264 [label=NativeLayerNormBackward0]
	2186726654592 -> 2186474767264
	2186726654592 [label=AddBackward0]
	2186996389632 -> 2186726654592
	2186996389632 [label=NativeDropoutBackward0]
	2186726602976 -> 2186996389632
	2186726602976 [label=ViewBackward0]
	2186726601488 -> 2186726602976
	2186726601488 [label=AddmmBackward0]
	2186474620624 -> 2186726601488
	2184990641344 [label="bert.encoder.layer.11.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990641344 -> 2186474620624
	2186474620624 [label=AccumulateGrad]
	2186996379456 -> 2186726601488
	2186996379456 [label=ViewBackward0]
	2186996376336 -> 2186996379456
	2186996376336 [label=ViewBackward0]
	2186726627744 -> 2186996376336
	2186726627744 [label=CloneBackward0]
	2186726626496 -> 2186726627744
	2186726626496 [label=PermuteBackward0]
	2186726629280 -> 2186726626496
	2186726629280 [label=UnsafeViewBackward0]
	2186726628416 -> 2186726629280
	2186726628416 [label=BmmBackward0]
	2186726626736 -> 2186726628416
	2186726626736 [label=ViewBackward0]
	2186726628752 -> 2186726626736
	2186726628752 [label=ExpandBackward0]
	2186726626160 -> 2186726628752
	2186726626160 [label=NativeDropoutBackward0]
	2186726626208 -> 2186726626160
	2186726626208 [label=SoftmaxBackward0]
	2186726625584 -> 2186726626208
	2186726625584 [label=AddBackward0]
	2186726626448 -> 2186726625584
	2186726626448 [label=DivBackward0]
	2186996496608 -> 2186726626448
	2186996496608 [label=UnsafeViewBackward0]
	2186996496560 -> 2186996496608
	2186996496560 [label=BmmBackward0]
	2186996497376 -> 2186996496560
	2186996497376 [label=UnsafeViewBackward0]
	2186726616752 -> 2186996497376
	2186726616752 [label=CloneBackward0]
	2186726616176 -> 2186726616752
	2186726616176 [label=ExpandBackward0]
	2186726616224 -> 2186726616176
	2186726616224 [label=PermuteBackward0]
	2186726615744 -> 2186726616224
	2186726615744 [label=ViewBackward0]
	2186726615312 -> 2186726615744
	2186726615312 [label=ViewBackward0]
	2186726615504 -> 2186726615312
	2186726615504 [label=AddmmBackward0]
	2186474631760 -> 2186726615504
	2184990655168 [label="bert.encoder.layer.11.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184990655168 -> 2186474631760
	2186474631760 [label=AccumulateGrad]
	2186726615072 -> 2186726615504
	2186726615072 [label=ViewBackward0]
	2186996391312 -> 2186726615072
	2186996391312 [label=NativeLayerNormBackward0]
	2186726613776 -> 2186996391312
	2186726613776 [label=AddBackward0]
	2186726614736 -> 2186726613776
	2186726614736 [label=NativeDropoutBackward0]
	2186996485904 -> 2186726614736
	2186996485904 [label=ViewBackward0]
	2186996403408 -> 2186996485904
	2186996403408 [label=AddmmBackward0]
	2186474632768 -> 2186996403408
	2184990656128 [label="bert.encoder.layer.10.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990656128 -> 2186474632768
	2186474632768 [label=AccumulateGrad]
	2186996404080 -> 2186996403408
	2186996404080 [label=ViewBackward0]
	2187268440512 -> 2186996404080
	2187268440512 [label=GeluBackward0]
	2187268440800 -> 2187268440512
	2187268440800 [label=ViewBackward0]
	2187268441904 -> 2187268440800
	2187268441904 [label=AddmmBackward0]
	2186474633392 -> 2187268441904
	2184990656608 [label="bert.encoder.layer.10.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184990656608 -> 2186474633392
	2186474633392 [label=AccumulateGrad]
	2187268442624 -> 2187268441904
	2187268442624 [label=ViewBackward0]
	2186726614400 -> 2187268442624
	2186726614400 [label=NativeLayerNormBackward0]
	2187268443728 -> 2186726614400
	2187268443728 [label=AddBackward0]
	2187268442384 -> 2187268443728
	2187268442384 [label=NativeDropoutBackward0]
	2187268125024 -> 2187268442384
	2187268125024 [label=ViewBackward0]
	2187268126080 -> 2187268125024
	2187268126080 [label=AddmmBackward0]
	2186474634304 -> 2187268126080
	2184990657568 [label="bert.encoder.layer.10.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990657568 -> 2186474634304
	2186474634304 [label=AccumulateGrad]
	2187268126032 -> 2187268126080
	2187268126032 [label=ViewBackward0]
	2187268126608 -> 2187268126032
	2187268126608 [label=ViewBackward0]
	2187268126800 -> 2187268126608
	2187268126800 [label=CloneBackward0]
	2187268127136 -> 2187268126800
	2187268127136 [label=PermuteBackward0]
	2187268127520 -> 2187268127136
	2187268127520 [label=UnsafeViewBackward0]
	2187268128144 -> 2187268127520
	2187268128144 [label=BmmBackward0]
	2187268127040 -> 2187268128144
	2187268127040 [label=ViewBackward0]
	2187268137120 -> 2187268127040
	2187268137120 [label=ExpandBackward0]
	2187268138176 -> 2187268137120
	2187268138176 [label=NativeDropoutBackward0]
	2187268138128 -> 2187268138176
	2187268138128 [label=SoftmaxBackward0]
	2187268138800 -> 2187268138128
	2187268138800 [label=AddBackward0]
	2187268138608 -> 2187268138800
	2187268138608 [label=DivBackward0]
	2187268138560 -> 2187268138608
	2187268138560 [label=UnsafeViewBackward0]
	2187268139424 -> 2187268138560
	2187268139424 [label=BmmBackward0]
	2187268139088 -> 2187268139424
	2187268139088 [label=UnsafeViewBackward0]
	2187268140672 -> 2187268139088
	2187268140672 [label=CloneBackward0]
	2187268154080 -> 2187268140672
	2187268154080 [label=ExpandBackward0]
	2187268154032 -> 2187268154080
	2187268154032 [label=PermuteBackward0]
	2187268154464 -> 2187268154032
	2187268154464 [label=ViewBackward0]
	2187268154896 -> 2187268154464
	2187268154896 [label=ViewBackward0]
	2187268154704 -> 2187268154896
	2187268154704 [label=AddmmBackward0]
	2186474641344 -> 2187268154704
	2184990671392 [label="bert.encoder.layer.10.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184990671392 -> 2186474641344
	2186474641344 [label=AccumulateGrad]
	2187268155088 -> 2187268154704
	2187268155088 [label=ViewBackward0]
	2187268443104 -> 2187268155088
	2187268443104 [label=NativeLayerNormBackward0]
	2187268155808 -> 2187268443104
	2187268155808 [label=AddBackward0]
	2187268156432 -> 2187268155808
	2187268156432 [label=NativeDropoutBackward0]
	2187268156864 -> 2187268156432
	2187268156864 [label=ViewBackward0]
	2187268165936 -> 2187268156864
	2187268165936 [label=AddmmBackward0]
	2186474642352 -> 2187268165936
	2184990672352 [label="bert.encoder.layer.9.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990672352 -> 2186474642352
	2186474642352 [label=AccumulateGrad]
	2187268166464 -> 2187268165936
	2187268166464 [label=ViewBackward0]
	2187268166560 -> 2187268166464
	2187268166560 [label=GeluBackward0]
	2187268166800 -> 2187268166560
	2187268166800 [label=ViewBackward0]
	2187268167184 -> 2187268166800
	2187268167184 [label=AddmmBackward0]
	2186474642976 -> 2187268167184
	2184990672832 [label="bert.encoder.layer.9.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184990672832 -> 2186474642976
	2186474642976 [label=AccumulateGrad]
	2187268167520 -> 2187268167184
	2187268167520 [label=ViewBackward0]
	2187268155280 -> 2187268167520
	2187268155280 [label=NativeLayerNormBackward0]
	2187268168528 -> 2187268155280
	2187268168528 [label=AddBackward0]
	2187268167424 -> 2187268168528
	2187268167424 [label=NativeDropoutBackward0]
	2187268178368 -> 2187268167424
	2187268178368 [label=ViewBackward0]
	2187268178944 -> 2187268178368
	2187268178944 [label=AddmmBackward0]
	2186474656240 -> 2187268178944
	2184990673792 [label="bert.encoder.layer.9.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990673792 -> 2186474656240
	2186474656240 [label=AccumulateGrad]
	2187268179040 -> 2187268178944
	2187268179040 [label=ViewBackward0]
	2187268179088 -> 2187268179040
	2187268179088 [label=ViewBackward0]
	2187268179280 -> 2187268179088
	2187268179280 [label=CloneBackward0]
	2187268179376 -> 2187268179280
	2187268179376 [label=PermuteBackward0]
	2187268179472 -> 2187268179376
	2187268179472 [label=UnsafeViewBackward0]
	2187268179568 -> 2187268179472
	2187268179568 [label=BmmBackward0]
	2187268179664 -> 2187268179568
	2187268179664 [label=ViewBackward0]
	2187268179808 -> 2187268179664
	2187268179808 [label=ExpandBackward0]
	2187268179904 -> 2187268179808
	2187268179904 [label=NativeDropoutBackward0]
	2187268180000 -> 2187268179904
	2187268180000 [label=SoftmaxBackward0]
	2187268180096 -> 2187268180000
	2187268180096 [label=AddBackward0]
	2187268180192 -> 2187268180096
	2187268180192 [label=DivBackward0]
	2187268180288 -> 2187268180192
	2187268180288 [label=UnsafeViewBackward0]
	2187268180384 -> 2187268180288
	2187268180384 [label=BmmBackward0]
	2187268180480 -> 2187268180384
	2187268180480 [label=UnsafeViewBackward0]
	2187268180624 -> 2187268180480
	2187268180624 [label=CloneBackward0]
	2187268180720 -> 2187268180624
	2187268180720 [label=ExpandBackward0]
	2187268180816 -> 2187268180720
	2187268180816 [label=PermuteBackward0]
	2187268180912 -> 2187268180816
	2187268180912 [label=ViewBackward0]
	2187268181008 -> 2187268180912
	2187268181008 [label=ViewBackward0]
	2187268181104 -> 2187268181008
	2187268181104 [label=AddmmBackward0]
	2186474659120 -> 2187268181104
	2184990691712 [label="bert.encoder.layer.9.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184990691712 -> 2186474659120
	2186474659120 [label=AccumulateGrad]
	2187268181200 -> 2187268181104
	2187268181200 [label=ViewBackward0]
	2187268169104 -> 2187268181200
	2187268169104 [label=NativeLayerNormBackward0]
	2187268181392 -> 2187268169104
	2187268181392 [label=AddBackward0]
	2187268181488 -> 2187268181392
	2187268181488 [label=NativeDropoutBackward0]
	2187268181632 -> 2187268181488
	2187268181632 [label=ViewBackward0]
	2187268181728 -> 2187268181632
	2187268181728 [label=AddmmBackward0]
	2186474664288 -> 2187268181728
	2184990692672 [label="bert.encoder.layer.8.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990692672 -> 2186474664288
	2186474664288 [label=AccumulateGrad]
	2187268181824 -> 2187268181728
	2187268181824 [label=ViewBackward0]
	2187268181872 -> 2187268181824
	2187268181872 [label=GeluBackward0]
	2187268181968 -> 2187268181872
	2187268181968 [label=ViewBackward0]
	2187024842960 -> 2187268181968
	2187024842960 [label=AddmmBackward0]
	2186474664912 -> 2187024842960
	2184990693392 [label="bert.encoder.layer.8.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184990693392 -> 2186474664912
	2186474664912 [label=AccumulateGrad]
	2187024843056 -> 2187024842960
	2187024843056 [label=ViewBackward0]
	2187268181440 -> 2187024843056
	2187268181440 [label=NativeLayerNormBackward0]
	2187024843248 -> 2187268181440
	2187024843248 [label=AddBackward0]
	2187024843344 -> 2187024843248
	2187024843344 [label=NativeDropoutBackward0]
	2187024843488 -> 2187024843344
	2187024843488 [label=ViewBackward0]
	2187024843584 -> 2187024843488
	2187024843584 [label=AddmmBackward0]
	2186474665824 -> 2187024843584
	2184990694352 [label="bert.encoder.layer.8.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990694352 -> 2186474665824
	2186474665824 [label=AccumulateGrad]
	2187024843680 -> 2187024843584
	2187024843680 [label=ViewBackward0]
	2187024843728 -> 2187024843680
	2187024843728 [label=ViewBackward0]
	2187024843920 -> 2187024843728
	2187024843920 [label=CloneBackward0]
	2187024844016 -> 2187024843920
	2187024844016 [label=PermuteBackward0]
	2187024844112 -> 2187024844016
	2187024844112 [label=UnsafeViewBackward0]
	2187024844208 -> 2187024844112
	2187024844208 [label=BmmBackward0]
	2187024844304 -> 2187024844208
	2187024844304 [label=ViewBackward0]
	2187024844448 -> 2187024844304
	2187024844448 [label=ExpandBackward0]
	2187024844544 -> 2187024844448
	2187024844544 [label=NativeDropoutBackward0]
	2187024844640 -> 2187024844544
	2187024844640 [label=SoftmaxBackward0]
	2187024844736 -> 2187024844640
	2187024844736 [label=AddBackward0]
	2187024844832 -> 2187024844736
	2187024844832 [label=DivBackward0]
	2187024844928 -> 2187024844832
	2187024844928 [label=UnsafeViewBackward0]
	2187024845024 -> 2187024844928
	2187024845024 [label=BmmBackward0]
	2187024845120 -> 2187024845024
	2187024845120 [label=UnsafeViewBackward0]
	2187024845264 -> 2187024845120
	2187024845264 [label=CloneBackward0]
	2187024845360 -> 2187024845264
	2187024845360 [label=ExpandBackward0]
	2187024845456 -> 2187024845360
	2187024845456 [label=PermuteBackward0]
	2187024845552 -> 2187024845456
	2187024845552 [label=ViewBackward0]
	2187024845648 -> 2187024845552
	2187024845648 [label=ViewBackward0]
	2187024845744 -> 2187024845648
	2187024845744 [label=AddmmBackward0]
	2186474676960 -> 2187024845744
	2184990712272 [label="bert.encoder.layer.8.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184990712272 -> 2186474676960
	2186474676960 [label=AccumulateGrad]
	2187024845840 -> 2187024845744
	2187024845840 [label=ViewBackward0]
	2187024843296 -> 2187024845840
	2187024843296 [label=NativeLayerNormBackward0]
	2187024846032 -> 2187024843296
	2187024846032 [label=AddBackward0]
	2187024846128 -> 2187024846032
	2187024846128 [label=NativeDropoutBackward0]
	2187024846272 -> 2187024846128
	2187024846272 [label=ViewBackward0]
	2187024846368 -> 2187024846272
	2187024846368 [label=AddmmBackward0]
	2186474677968 -> 2187024846368
	2184990713232 [label="bert.encoder.layer.7.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990713232 -> 2186474677968
	2186474677968 [label=AccumulateGrad]
	2187024846464 -> 2187024846368
	2187024846464 [label=ViewBackward0]
	2187024846512 -> 2187024846464
	2187024846512 [label=GeluBackward0]
	2187024846704 -> 2187024846512
	2187024846704 [label=ViewBackward0]
	2187024846800 -> 2187024846704
	2187024846800 [label=AddmmBackward0]
	2186474678592 -> 2187024846800
	2184990713712 [label="bert.encoder.layer.7.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184990713712 -> 2186474678592
	2186474678592 [label=AccumulateGrad]
	2187024846608 -> 2187024846800
	2187024846608 [label=ViewBackward0]
	2187024846080 -> 2187024846608
	2187024846080 [label=NativeLayerNormBackward0]
	2187024855344 -> 2187024846080
	2187024855344 [label=AddBackward0]
	2187024855440 -> 2187024855344
	2187024855440 [label=NativeDropoutBackward0]
	2187024855584 -> 2187024855440
	2187024855584 [label=ViewBackward0]
	2187024855680 -> 2187024855584
	2187024855680 [label=AddmmBackward0]
	2186474679504 -> 2187024855680
	2184990714672 [label="bert.encoder.layer.7.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990714672 -> 2186474679504
	2186474679504 [label=AccumulateGrad]
	2187024855776 -> 2187024855680
	2187024855776 [label=ViewBackward0]
	2187024855824 -> 2187024855776
	2187024855824 [label=ViewBackward0]
	2187024856016 -> 2187024855824
	2187024856016 [label=CloneBackward0]
	2187024856112 -> 2187024856016
	2187024856112 [label=PermuteBackward0]
	2187024856208 -> 2187024856112
	2187024856208 [label=UnsafeViewBackward0]
	2187024856304 -> 2187024856208
	2187024856304 [label=BmmBackward0]
	2187024856400 -> 2187024856304
	2187024856400 [label=ViewBackward0]
	2187024856544 -> 2187024856400
	2187024856544 [label=ExpandBackward0]
	2187024856640 -> 2187024856544
	2187024856640 [label=NativeDropoutBackward0]
	2187024856736 -> 2187024856640
	2187024856736 [label=SoftmaxBackward0]
	2187024856832 -> 2187024856736
	2187024856832 [label=AddBackward0]
	2187024856928 -> 2187024856832
	2187024856928 [label=DivBackward0]
	2187024857024 -> 2187024856928
	2187024857024 [label=UnsafeViewBackward0]
	2187024857120 -> 2187024857024
	2187024857120 [label=BmmBackward0]
	2187024857216 -> 2187024857120
	2187024857216 [label=UnsafeViewBackward0]
	2187024857360 -> 2187024857216
	2187024857360 [label=CloneBackward0]
	2187024857456 -> 2187024857360
	2187024857456 [label=ExpandBackward0]
	2187024857552 -> 2187024857456
	2187024857552 [label=PermuteBackward0]
	2187024857648 -> 2187024857552
	2187024857648 [label=ViewBackward0]
	2187024857744 -> 2187024857648
	2187024857744 [label=ViewBackward0]
	2187024857840 -> 2187024857744
	2187024857840 [label=AddmmBackward0]
	2186996471984 -> 2187024857840
	2184990732592 [label="bert.encoder.layer.7.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184990732592 -> 2186996471984
	2186996471984 [label=AccumulateGrad]
	2187024857936 -> 2187024857840
	2187024857936 [label=ViewBackward0]
	2187024855392 -> 2187024857936
	2187024855392 [label=NativeLayerNormBackward0]
	2187024858128 -> 2187024855392
	2187024858128 [label=AddBackward0]
	2187024858224 -> 2187024858128
	2187024858224 [label=NativeDropoutBackward0]
	2187024858368 -> 2187024858224
	2187024858368 [label=ViewBackward0]
	2187024858464 -> 2187024858368
	2187024858464 [label=AddmmBackward0]
	2186996471072 -> 2187024858464
	2184990733552 [label="bert.encoder.layer.6.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990733552 -> 2186996471072
	2186996471072 [label=AccumulateGrad]
	2187024858560 -> 2187024858464
	2187024858560 [label=ViewBackward0]
	2187024858608 -> 2187024858560
	2187024858608 [label=GeluBackward0]
	2187024858800 -> 2187024858608
	2187024858800 [label=ViewBackward0]
	2187024858896 -> 2187024858800
	2187024858896 [label=AddmmBackward0]
	2186996470544 -> 2187024858896
	2184990734032 [label="bert.encoder.layer.6.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184990734032 -> 2186996470544
	2186996470544 [label=AccumulateGrad]
	2187024858992 -> 2187024858896
	2187024858992 [label=ViewBackward0]
	2187024858176 -> 2187024858992
	2187024858176 [label=NativeLayerNormBackward0]
	2187024859088 -> 2187024858176
	2187024859088 [label=AddBackward0]
	2187024867536 -> 2187024859088
	2187024867536 [label=NativeDropoutBackward0]
	2187024867680 -> 2187024867536
	2187024867680 [label=ViewBackward0]
	2187024867776 -> 2187024867680
	2187024867776 [label=AddmmBackward0]
	2186996457232 -> 2187024867776
	2184990734992 [label="bert.encoder.layer.6.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990734992 -> 2186996457232
	2186996457232 [label=AccumulateGrad]
	2187024867872 -> 2187024867776
	2187024867872 [label=ViewBackward0]
	2187024867920 -> 2187024867872
	2187024867920 [label=ViewBackward0]
	2187024868112 -> 2187024867920
	2187024868112 [label=CloneBackward0]
	2187024868208 -> 2187024868112
	2187024868208 [label=PermuteBackward0]
	2187024868304 -> 2187024868208
	2187024868304 [label=UnsafeViewBackward0]
	2187024868400 -> 2187024868304
	2187024868400 [label=BmmBackward0]
	2187024868496 -> 2187024868400
	2187024868496 [label=ViewBackward0]
	2187024868640 -> 2187024868496
	2187024868640 [label=ExpandBackward0]
	2187024868736 -> 2187024868640
	2187024868736 [label=NativeDropoutBackward0]
	2187024868832 -> 2187024868736
	2187024868832 [label=SoftmaxBackward0]
	2187024868928 -> 2187024868832
	2187024868928 [label=AddBackward0]
	2187024869024 -> 2187024868928
	2187024869024 [label=DivBackward0]
	2187024869120 -> 2187024869024
	2187024869120 [label=UnsafeViewBackward0]
	2187024869216 -> 2187024869120
	2187024869216 [label=BmmBackward0]
	2187024869312 -> 2187024869216
	2187024869312 [label=UnsafeViewBackward0]
	2187024869456 -> 2187024869312
	2187024869456 [label=CloneBackward0]
	2187024869552 -> 2187024869456
	2187024869552 [label=ExpandBackward0]
	2187024869648 -> 2187024869552
	2187024869648 [label=PermuteBackward0]
	2187024869744 -> 2187024869648
	2187024869744 [label=ViewBackward0]
	2187024869840 -> 2187024869744
	2187024869840 [label=ViewBackward0]
	2187024869936 -> 2187024869840
	2187024869936 [label=AddmmBackward0]
	2186996454256 -> 2187024869936
	2184990748816 [label="bert.encoder.layer.6.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184990748816 -> 2186996454256
	2186996454256 [label=AccumulateGrad]
	2187024870032 -> 2187024869936
	2187024870032 [label=ViewBackward0]
	2187024867488 -> 2187024870032
	2187024867488 [label=NativeLayerNormBackward0]
	2187024870224 -> 2187024867488
	2187024870224 [label=AddBackward0]
	2187024870320 -> 2187024870224
	2187024870320 [label=NativeDropoutBackward0]
	2187024870464 -> 2187024870320
	2187024870464 [label=ViewBackward0]
	2187024870560 -> 2187024870464
	2187024870560 [label=AddmmBackward0]
	2186996444992 -> 2187024870560
	2184990750016 [label="bert.encoder.layer.5.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990750016 -> 2186996444992
	2186996444992 [label=AccumulateGrad]
	2187024870656 -> 2187024870560
	2187024870656 [label=ViewBackward0]
	2187024870704 -> 2187024870656
	2187024870704 [label=GeluBackward0]
	2187024870896 -> 2187024870704
	2187024870896 [label=ViewBackward0]
	2187024870992 -> 2187024870896
	2187024870992 [label=AddmmBackward0]
	2186996444128 -> 2187024870992
	2184990750496 [label="bert.encoder.layer.5.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184990750496 -> 2186996444128
	2186996444128 [label=AccumulateGrad]
	2187024871088 -> 2187024870992
	2187024871088 [label=ViewBackward0]
	2187024870272 -> 2187024871088
	2187024870272 [label=NativeLayerNormBackward0]
	2187024871280 -> 2187024870272
	2187024871280 [label=AddBackward0]
	2187024871376 -> 2187024871280
	2187024871376 [label=NativeDropoutBackward0]
	2187024883872 -> 2187024871376
	2187024883872 [label=ViewBackward0]
	2187024883968 -> 2187024883872
	2187024883968 [label=AddmmBackward0]
	2186996443072 -> 2187024883968
	2184990751536 [label="bert.encoder.layer.5.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184990751536 -> 2186996443072
	2186996443072 [label=AccumulateGrad]
	2187024884064 -> 2187024883968
	2187024884064 [label=ViewBackward0]
	2187024884112 -> 2187024884064
	2187024884112 [label=ViewBackward0]
	2187024884304 -> 2187024884112
	2187024884304 [label=CloneBackward0]
	2187024884400 -> 2187024884304
	2187024884400 [label=PermuteBackward0]
	2187024884496 -> 2187024884400
	2187024884496 [label=UnsafeViewBackward0]
	2187024884592 -> 2187024884496
	2187024884592 [label=BmmBackward0]
	2187024884688 -> 2187024884592
	2187024884688 [label=ViewBackward0]
	2187024884832 -> 2187024884688
	2187024884832 [label=ExpandBackward0]
	2187024884928 -> 2187024884832
	2187024884928 [label=NativeDropoutBackward0]
	2187024885024 -> 2187024884928
	2187024885024 [label=SoftmaxBackward0]
	2187024885120 -> 2187024885024
	2187024885120 [label=AddBackward0]
	2187024885216 -> 2187024885120
	2187024885216 [label=DivBackward0]
	2187024885312 -> 2187024885216
	2187024885312 [label=UnsafeViewBackward0]
	2187024885408 -> 2187024885312
	2187024885408 [label=BmmBackward0]
	2187024885504 -> 2187024885408
	2187024885504 [label=UnsafeViewBackward0]
	2187024885648 -> 2187024885504
	2187024885648 [label=CloneBackward0]
	2187024885744 -> 2187024885648
	2187024885744 [label=ExpandBackward0]
	2187024885840 -> 2187024885744
	2187024885840 [label=PermuteBackward0]
	2187024885936 -> 2187024885840
	2187024885936 [label=ViewBackward0]
	2187024886032 -> 2187024885936
	2187024886032 [label=ViewBackward0]
	2187024886128 -> 2187024886032
	2187024886128 [label=AddmmBackward0]
	2186996431936 -> 2187024886128
	2184990769376 [label="bert.encoder.layer.5.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184990769376 -> 2186996431936
	2186996431936 [label=AccumulateGrad]
	2187024886224 -> 2187024886128
	2187024886224 [label=ViewBackward0]
	2187024871328 -> 2187024886224
	2187024871328 [label=NativeLayerNormBackward0]
	2187024886416 -> 2187024871328
	2187024886416 [label=AddBackward0]
	2187024886512 -> 2187024886416
	2187024886512 [label=NativeDropoutBackward0]
	2187024886656 -> 2187024886512
	2187024886656 [label=ViewBackward0]
	2187024886752 -> 2187024886656
	2187024886752 [label=AddmmBackward0]
	2186996430880 -> 2187024886752
	2184890628992 [label="bert.encoder.layer.4.output.dense.bias
 (768)" fillcolor=lightblue]
	2184890628992 -> 2186996430880
	2186996430880 [label=AccumulateGrad]
	2187024886848 -> 2187024886752
	2187024886848 [label=ViewBackward0]
	2187024886896 -> 2187024886848
	2187024886896 [label=GeluBackward0]
	2187024887088 -> 2187024886896
	2187024887088 [label=ViewBackward0]
	2187024887184 -> 2187024887088
	2187024887184 [label=AddmmBackward0]
	2186996430352 -> 2187024887184
	2184890629312 [label="bert.encoder.layer.4.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184890629312 -> 2186996430352
	2186996430352 [label=AccumulateGrad]
	2187024887280 -> 2187024887184
	2187024887280 [label=ViewBackward0]
	2187024886464 -> 2187024887280
	2187024886464 [label=NativeLayerNormBackward0]
	2187024887472 -> 2187024886464
	2187024887472 [label=AddBackward0]
	2187024887568 -> 2187024887472
	2187024887568 [label=NativeDropoutBackward0]
	2187024887712 -> 2187024887568
	2187024887712 [label=ViewBackward0]
	2187024887760 -> 2187024887712
	2187024887760 [label=AddmmBackward0]
	2186996429488 -> 2187024887760
	2184890629872 [label="bert.encoder.layer.4.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184890629872 -> 2186996429488
	2186996429488 [label=AccumulateGrad]
	2187024896160 -> 2187024887760
	2187024896160 [label=ViewBackward0]
	2187024896208 -> 2187024896160
	2187024896208 [label=ViewBackward0]
	2187024896400 -> 2187024896208
	2187024896400 [label=CloneBackward0]
	2187024896496 -> 2187024896400
	2187024896496 [label=PermuteBackward0]
	2187024896592 -> 2187024896496
	2187024896592 [label=UnsafeViewBackward0]
	2187024896688 -> 2187024896592
	2187024896688 [label=BmmBackward0]
	2187024896784 -> 2187024896688
	2187024896784 [label=ViewBackward0]
	2187024896928 -> 2187024896784
	2187024896928 [label=ExpandBackward0]
	2187024897024 -> 2187024896928
	2187024897024 [label=NativeDropoutBackward0]
	2187024897120 -> 2187024897024
	2187024897120 [label=SoftmaxBackward0]
	2187024897216 -> 2187024897120
	2187024897216 [label=AddBackward0]
	2187024897312 -> 2187024897216
	2187024897312 [label=DivBackward0]
	2187024897408 -> 2187024897312
	2187024897408 [label=UnsafeViewBackward0]
	2187024897504 -> 2187024897408
	2187024897504 [label=BmmBackward0]
	2187024897600 -> 2187024897504
	2187024897600 [label=UnsafeViewBackward0]
	2187024897744 -> 2187024897600
	2187024897744 [label=CloneBackward0]
	2187024897840 -> 2187024897744
	2187024897840 [label=ExpandBackward0]
	2187024897936 -> 2187024897840
	2187024897936 [label=PermuteBackward0]
	2187024898032 -> 2187024897936
	2187024898032 [label=ViewBackward0]
	2187024898128 -> 2187024898032
	2187024898128 [label=ViewBackward0]
	2187024898224 -> 2187024898128
	2187024898224 [label=AddmmBackward0]
	2186996418160 -> 2187024898224
	2184890638480 [label="bert.encoder.layer.4.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184890638480 -> 2186996418160
	2186996418160 [label=AccumulateGrad]
	2187024898320 -> 2187024898224
	2187024898320 [label=ViewBackward0]
	2187024887520 -> 2187024898320
	2187024887520 [label=NativeLayerNormBackward0]
	2187024898512 -> 2187024887520
	2187024898512 [label=AddBackward0]
	2187024898608 -> 2187024898512
	2187024898608 [label=NativeDropoutBackward0]
	2187024898752 -> 2187024898608
	2187024898752 [label=ViewBackward0]
	2187024898848 -> 2187024898752
	2187024898848 [label=AddmmBackward0]
	2186996417296 -> 2187024898848
	2184890638960 [label="bert.encoder.layer.3.output.dense.bias
 (768)" fillcolor=lightblue]
	2184890638960 -> 2186996417296
	2186996417296 [label=AccumulateGrad]
	2187024898944 -> 2187024898848
	2187024898944 [label=ViewBackward0]
	2187024898992 -> 2187024898944
	2187024898992 [label=GeluBackward0]
	2187024899184 -> 2187024898992
	2187024899184 [label=ViewBackward0]
	2187024899280 -> 2187024899184
	2187024899280 [label=AddmmBackward0]
	2186996416624 -> 2187024899280
	2184890639440 [label="bert.encoder.layer.3.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184890639440 -> 2186996416624
	2186996416624 [label=AccumulateGrad]
	2187024899376 -> 2187024899280
	2187024899376 [label=ViewBackward0]
	2187024898560 -> 2187024899376
	2187024898560 [label=NativeLayerNormBackward0]
	2187024899568 -> 2187024898560
	2187024899568 [label=AddBackward0]
	2187024899664 -> 2187024899568
	2187024899664 [label=NativeDropoutBackward0]
	2187024899808 -> 2187024899664
	2187024899808 [label=ViewBackward0]
	2187024899904 -> 2187024899808
	2187024899904 [label=AddmmBackward0]
	2186996402880 -> 2187024899904
	2184890640080 [label="bert.encoder.layer.3.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184890640080 -> 2186996402880
	2186996402880 [label=AccumulateGrad]
	2187024900000 -> 2187024899904
	2187024900000 [label=ViewBackward0]
	2187024900048 -> 2187024900000
	2187024900048 [label=ViewBackward0]
	2187024908496 -> 2187024900048
	2187024908496 [label=CloneBackward0]
	2187024908592 -> 2187024908496
	2187024908592 [label=PermuteBackward0]
	2187024908688 -> 2187024908592
	2187024908688 [label=UnsafeViewBackward0]
	2187024908784 -> 2187024908688
	2187024908784 [label=BmmBackward0]
	2187024908880 -> 2187024908784
	2187024908880 [label=ViewBackward0]
	2187024909024 -> 2187024908880
	2187024909024 [label=ExpandBackward0]
	2187024909120 -> 2187024909024
	2187024909120 [label=NativeDropoutBackward0]
	2187024909216 -> 2187024909120
	2187024909216 [label=SoftmaxBackward0]
	2187024909312 -> 2187024909216
	2187024909312 [label=AddBackward0]
	2187024909408 -> 2187024909312
	2187024909408 [label=DivBackward0]
	2187024909504 -> 2187024909408
	2187024909504 [label=UnsafeViewBackward0]
	2187024909600 -> 2187024909504
	2187024909600 [label=BmmBackward0]
	2187024909696 -> 2187024909600
	2187024909696 [label=UnsafeViewBackward0]
	2187024909840 -> 2187024909696
	2187024909840 [label=CloneBackward0]
	2187024909936 -> 2187024909840
	2187024909936 [label=ExpandBackward0]
	2187024910032 -> 2187024909936
	2187024910032 [label=PermuteBackward0]
	2187024910128 -> 2187024910032
	2187024910128 [label=ViewBackward0]
	2187024910224 -> 2187024910128
	2187024910224 [label=ViewBackward0]
	2187024910320 -> 2187024910224
	2187024910320 [label=AddmmBackward0]
	2186996400288 -> 2187024910320
	2184890643136 [label="bert.encoder.layer.3.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184890643136 -> 2186996400288
	2186996400288 [label=AccumulateGrad]
	2187024910416 -> 2187024910320
	2187024910416 [label=ViewBackward0]
	2187024899616 -> 2187024910416
	2187024899616 [label=NativeLayerNormBackward0]
	2187024910608 -> 2187024899616
	2187024910608 [label=AddBackward0]
	2187024910704 -> 2187024910608
	2187024910704 [label=NativeDropoutBackward0]
	2187024910848 -> 2187024910704
	2187024910848 [label=ViewBackward0]
	2187024910944 -> 2187024910848
	2187024910944 [label=AddmmBackward0]
	2186996390688 -> 2187024910944
	2184890643616 [label="bert.encoder.layer.2.output.dense.bias
 (768)" fillcolor=lightblue]
	2184890643616 -> 2186996390688
	2186996390688 [label=AccumulateGrad]
	2187024911040 -> 2187024910944
	2187024911040 [label=ViewBackward0]
	2187024911088 -> 2187024911040
	2187024911088 [label=GeluBackward0]
	2187024911280 -> 2187024911088
	2187024911280 [label=ViewBackward0]
	2187024911376 -> 2187024911280
	2187024911376 [label=AddmmBackward0]
	2186996390208 -> 2187024911376
	2184890644096 [label="bert.encoder.layer.2.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184890644096 -> 2186996390208
	2186996390208 [label=AccumulateGrad]
	2187024911472 -> 2187024911376
	2187024911472 [label=ViewBackward0]
	2187024910656 -> 2187024911472
	2187024910656 [label=NativeLayerNormBackward0]
	2187024911664 -> 2187024910656
	2187024911664 [label=AddBackward0]
	2187024911760 -> 2187024911664
	2187024911760 [label=NativeDropoutBackward0]
	2187024911904 -> 2187024911760
	2187024911904 [label=ViewBackward0]
	2187024912000 -> 2187024911904
	2187024912000 [label=AddmmBackward0]
	2186996389344 -> 2187024912000
	2184890644576 [label="bert.encoder.layer.2.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184890644576 -> 2186996389344
	2186996389344 [label=AccumulateGrad]
	2187024912096 -> 2187024912000
	2187024912096 [label=ViewBackward0]
	2187024912144 -> 2187024912096
	2187024912144 [label=ViewBackward0]
	2187024912336 -> 2187024912144
	2187024912336 [label=CloneBackward0]
	2187024912240 -> 2187024912336
	2187024912240 [label=PermuteBackward0]
	2187024920784 -> 2187024912240
	2187024920784 [label=UnsafeViewBackward0]
	2187024920880 -> 2187024920784
	2187024920880 [label=BmmBackward0]
	2187024920976 -> 2187024920880
	2187024920976 [label=ViewBackward0]
	2187024921120 -> 2187024920976
	2187024921120 [label=ExpandBackward0]
	2187024921216 -> 2187024921120
	2187024921216 [label=NativeDropoutBackward0]
	2187024921312 -> 2187024921216
	2187024921312 [label=SoftmaxBackward0]
	2187024921408 -> 2187024921312
	2187024921408 [label=AddBackward0]
	2187024921504 -> 2187024921408
	2187024921504 [label=DivBackward0]
	2187024921600 -> 2187024921504
	2187024921600 [label=UnsafeViewBackward0]
	2187024921696 -> 2187024921600
	2187024921696 [label=BmmBackward0]
	2187024921792 -> 2187024921696
	2187024921792 [label=UnsafeViewBackward0]
	2187024921936 -> 2187024921792
	2187024921936 [label=CloneBackward0]
	2187024922032 -> 2187024921936
	2187024922032 [label=ExpandBackward0]
	2187024922128 -> 2187024922032
	2187024922128 [label=PermuteBackward0]
	2187024922224 -> 2187024922128
	2187024922224 [label=ViewBackward0]
	2187024922320 -> 2187024922224
	2187024922320 [label=ViewBackward0]
	2187024922416 -> 2187024922320
	2187024922416 [label=AddmmBackward0]
	2186996378016 -> 2187024922416
	2184890645536 [label="bert.encoder.layer.2.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184890645536 -> 2186996378016
	2186996378016 [label=AccumulateGrad]
	2187024922512 -> 2187024922416
	2187024922512 [label=ViewBackward0]
	2187024911712 -> 2187024922512
	2187024911712 [label=NativeLayerNormBackward0]
	2187024922704 -> 2187024911712
	2187024922704 [label=AddBackward0]
	2187024922800 -> 2187024922704
	2187024922800 [label=NativeDropoutBackward0]
	2187024922944 -> 2187024922800
	2187024922944 [label=ViewBackward0]
	2187024923040 -> 2187024922944
	2187024923040 [label=AddmmBackward0]
	2186996377104 -> 2187024923040
	2184890646176 [label="bert.encoder.layer.1.output.dense.bias
 (768)" fillcolor=lightblue]
	2184890646176 -> 2186996377104
	2186996377104 [label=AccumulateGrad]
	2187024923136 -> 2187024923040
	2187024923136 [label=ViewBackward0]
	2187024923184 -> 2187024923136
	2187024923184 [label=GeluBackward0]
	2187024923376 -> 2187024923184
	2187024923376 [label=ViewBackward0]
	2187024923472 -> 2187024923376
	2187024923472 [label=AddmmBackward0]
	2186996376528 -> 2187024923472
	2184890578080 [label="bert.encoder.layer.1.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184890578080 -> 2186996376528
	2186996376528 [label=AccumulateGrad]
	2187024923568 -> 2187024923472
	2187024923568 [label=ViewBackward0]
	2187024922752 -> 2187024923568
	2187024922752 [label=NativeLayerNormBackward0]
	2187024923760 -> 2187024922752
	2187024923760 [label=AddBackward0]
	2187024923856 -> 2187024923760
	2187024923856 [label=NativeDropoutBackward0]
	2187024924000 -> 2187024923856
	2187024924000 [label=ViewBackward0]
	2187024924096 -> 2187024924000
	2187024924096 [label=AddmmBackward0]
	2186996496320 -> 2187024924096
	2184890577600 [label="bert.encoder.layer.1.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184890577600 -> 2186996496320
	2186996496320 [label=AccumulateGrad]
	2187024924192 -> 2187024924096
	2187024924192 [label=ViewBackward0]
	2187024924240 -> 2187024924192
	2187024924240 [label=ViewBackward0]
	2187024924432 -> 2187024924240
	2187024924432 [label=CloneBackward0]
	2187024924528 -> 2187024924432
	2187024924528 [label=PermuteBackward0]
	2187024924624 -> 2187024924528
	2187024924624 [label=UnsafeViewBackward0]
	2187024924336 -> 2187024924624
	2187024924336 [label=BmmBackward0]
	2187024937168 -> 2187024924336
	2187024937168 [label=ViewBackward0]
	2187024937312 -> 2187024937168
	2187024937312 [label=ExpandBackward0]
	2187024937408 -> 2187024937312
	2187024937408 [label=NativeDropoutBackward0]
	2187024937504 -> 2187024937408
	2187024937504 [label=SoftmaxBackward0]
	2187024937600 -> 2187024937504
	2187024937600 [label=AddBackward0]
	2187024937696 -> 2187024937600
	2187024937696 [label=DivBackward0]
	2187024937792 -> 2187024937696
	2187024937792 [label=UnsafeViewBackward0]
	2187024937888 -> 2187024937792
	2187024937888 [label=BmmBackward0]
	2187024937984 -> 2187024937888
	2187024937984 [label=UnsafeViewBackward0]
	2187024938128 -> 2187024937984
	2187024938128 [label=CloneBackward0]
	2187024938224 -> 2187024938128
	2187024938224 [label=ExpandBackward0]
	2187024938320 -> 2187024938224
	2187024938320 [label=PermuteBackward0]
	2187024938416 -> 2187024938320
	2187024938416 [label=ViewBackward0]
	2187024938512 -> 2187024938416
	2187024938512 [label=ViewBackward0]
	2187024938608 -> 2187024938512
	2187024938608 [label=AddmmBackward0]
	2186996485520 -> 2187024938608
	2184980188832 [label="bert.encoder.layer.1.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184980188832 -> 2186996485520
	2186996485520 [label=AccumulateGrad]
	2187024938704 -> 2187024938608
	2187024938704 [label=ViewBackward0]
	2187024923808 -> 2187024938704
	2187024923808 [label=NativeLayerNormBackward0]
	2187024938896 -> 2187024923808
	2187024938896 [label=AddBackward0]
	2187024938992 -> 2187024938896
	2187024938992 [label=NativeDropoutBackward0]
	2187024939136 -> 2187024938992
	2187024939136 [label=ViewBackward0]
	2187024939232 -> 2187024939136
	2187024939232 [label=AddmmBackward0]
	2186996484272 -> 2187024939232
	2184985072720 [label="bert.encoder.layer.0.output.dense.bias
 (768)" fillcolor=lightblue]
	2184985072720 -> 2186996484272
	2186996484272 [label=AccumulateGrad]
	2187024939328 -> 2187024939232
	2187024939328 [label=ViewBackward0]
	2187024939376 -> 2187024939328
	2187024939376 [label=GeluBackward0]
	2187024939568 -> 2187024939376
	2187024939568 [label=ViewBackward0]
	2187024939664 -> 2187024939568
	2187024939664 [label=AddmmBackward0]
	2186996483552 -> 2187024939664
	2184989269344 [label="bert.encoder.layer.0.intermediate.dense.bias
 (3072)" fillcolor=lightblue]
	2184989269344 -> 2186996483552
	2186996483552 [label=AccumulateGrad]
	2187024939760 -> 2187024939664
	2187024939760 [label=ViewBackward0]
	2187024938944 -> 2187024939760
	2187024938944 [label=NativeLayerNormBackward0]
	2187024939952 -> 2187024938944
	2187024939952 [label=AddBackward0]
	2187024940048 -> 2187024939952
	2187024940048 [label=NativeDropoutBackward0]
	2187024940192 -> 2187024940048
	2187024940192 [label=ViewBackward0]
	2187024940288 -> 2187024940192
	2187024940288 [label=AddmmBackward0]
	2186996482736 -> 2187024940288
	2184985250128 [label="bert.encoder.layer.0.attention.output.dense.bias
 (768)" fillcolor=lightblue]
	2184985250128 -> 2186996482736
	2186996482736 [label=AccumulateGrad]
	2187024940384 -> 2187024940288
	2187024940384 [label=ViewBackward0]
	2187024940432 -> 2187024940384
	2187024940432 [label=ViewBackward0]
	2187024940624 -> 2187024940432
	2187024940624 [label=CloneBackward0]
	2187024940720 -> 2187024940624
	2187024940720 [label=PermuteBackward0]
	2187024940816 -> 2187024940720
	2187024940816 [label=UnsafeViewBackward0]
	2187024940912 -> 2187024940816
	2187024940912 [label=BmmBackward0]
	2187024941008 -> 2187024940912
	2187024941008 [label=ViewBackward0]
	2187024949408 -> 2187024941008
	2187024949408 [label=ExpandBackward0]
	2187024949504 -> 2187024949408
	2187024949504 [label=NativeDropoutBackward0]
	2187024949600 -> 2187024949504
	2187024949600 [label=SoftmaxBackward0]
	2187024949696 -> 2187024949600
	2187024949696 [label=AddBackward0]
	2187024949792 -> 2187024949696
	2187024949792 [label=DivBackward0]
	2187024949888 -> 2187024949792
	2187024949888 [label=UnsafeViewBackward0]
	2187024949984 -> 2187024949888
	2187024949984 [label=BmmBackward0]
	2187024950080 -> 2187024949984
	2187024950080 [label=UnsafeViewBackward0]
	2187024950224 -> 2187024950080
	2187024950224 [label=CloneBackward0]
	2187024950320 -> 2187024950224
	2187024950320 [label=ExpandBackward0]
	2187024950416 -> 2187024950320
	2187024950416 [label=PermuteBackward0]
	2187024950512 -> 2187024950416
	2187024950512 [label=ViewBackward0]
	2187024950608 -> 2187024950512
	2187024950608 [label=ViewBackward0]
	2187024950704 -> 2187024950608
	2187024950704 [label=AddmmBackward0]
	2186726603216 -> 2187024950704
	2184989313040 [label="bert.encoder.layer.0.attention.self.query.bias
 (768)" fillcolor=lightblue]
	2184989313040 -> 2186726603216
	2186726603216 [label=AccumulateGrad]
	2187024950800 -> 2187024950704
	2187024950800 [label=ViewBackward0]
	2187024940000 -> 2187024950800
	2187024940000 [label=NativeDropoutBackward0]
	2187024950992 -> 2187024940000
	2187024950992 [label=NativeLayerNormBackward0]
	2187024951088 -> 2187024950992
	2187024951088 [label=AddBackward0]
	2187024951184 -> 2187024951088
	2187024951184 [label=AddBackward0]
	2187024951328 -> 2187024951184
	2187024951328 [label=ViewBackward0]
	2187024951472 -> 2187024951328
	2187024951472 [label=AddmmBackward0]
	2186726602064 -> 2187024951472
	2184989274256 [label="bert.embeddings.map_fc.bias
 (768)" fillcolor=lightblue]
	2184989274256 -> 2186726602064
	2186726602064 [label=AccumulateGrad]
	2187024951568 -> 2187024951472
	2187024951568 [label=ViewBackward0]
	2187024951616 -> 2187024951568
	2187024951616 [label=CatBackward0]
	2187024951808 -> 2187024951616
	2187024951808 [label=EmbeddingBackward0]
	2186726601200 -> 2187024951808
	2184989313200 [label="bert.embeddings.word_embeddings.weight
 (23236, 768)" fillcolor=lightblue]
	2184989313200 -> 2186726601200
	2186726601200 [label=AccumulateGrad]
	2187024951760 -> 2187024951616
	2187024951760 [label=ViewBackward0]
	2187024951904 -> 2187024951760
	2187024951904 [label=SqueezeBackward1]
	2187024952096 -> 2187024951904
	2187024952096 [label=MaxPool2DWithIndicesBackward0]
	2187024952192 -> 2187024952096
	2187024952192 [label=UnsqueezeBackward0]
	2187024952288 -> 2187024952192
	2187024952288 [label=ConvolutionBackward0]
	2187024952384 -> 2187024952288
	2187024952384 [label=PermuteBackward0]
	2187024952480 -> 2187024952384
	2187024952480 [label=ViewBackward0]
	2187024952576 -> 2187024952480
	2187024952576 [label=EmbeddingBackward0]
	2186917575840 -> 2187024952576
	2184971136512 [label="bert.embeddings.pinyin_embeddings.embedding.weight
 (32, 128)" fillcolor=lightblue]
	2184971136512 -> 2186917575840
	2186917575840 [label=AccumulateGrad]
	2186917576416 -> 2187024952288
	2184988368144 [label="bert.embeddings.pinyin_embeddings.conv.weight
 (768, 128, 2)" fillcolor=lightblue]
	2184988368144 -> 2186917576416
	2186917576416 [label=AccumulateGrad]
	2186917576656 -> 2187024952288
	2184986547200 [label="bert.embeddings.pinyin_embeddings.conv.bias
 (768)" fillcolor=lightblue]
	2184986547200 -> 2186917576656
	2186917576656 [label=AccumulateGrad]
	2187024951712 -> 2187024951616
	2187024951712 [label=ViewBackward0]
	2187024952144 -> 2187024951712
	2187024952144 [label=AddmmBackward0]
	2186726600960 -> 2187024952144
	2184983900224 [label="bert.embeddings.glyph_map.bias
 (768)" fillcolor=lightblue]
	2184983900224 -> 2186726600960
	2186726600960 [label=AccumulateGrad]
	2187024952336 -> 2187024952144
	2187024952336 [label=ViewBackward0]
	2187024952432 -> 2187024952336
	2187024952432 [label=EmbeddingBackward0]
	2186917576128 -> 2187024952432
	2184979947904 [label="bert.embeddings.glyph_embeddings.embedding.weight
 (23236, 1728)" fillcolor=lightblue]
	2184979947904 -> 2186917576128
	2186917576128 [label=AccumulateGrad]
	2187024952240 -> 2187024952144
	2187024952240 [label=TBackward0]
	2186917575648 -> 2187024952240
	2184985859312 [label="bert.embeddings.glyph_map.weight
 (768, 1728)" fillcolor=lightblue]
	2184985859312 -> 2186917575648
	2186917575648 [label=AccumulateGrad]
	2187024951520 -> 2187024951472
	2187024951520 [label=TBackward0]
	2186726601296 -> 2187024951520
	2184985056096 [label="bert.embeddings.map_fc.weight
 (768, 2304)" fillcolor=lightblue]
	2184985056096 -> 2186726601296
	2186726601296 [label=AccumulateGrad]
	2187024951280 -> 2187024951184
	2187024951280 [label=EmbeddingBackward0]
	2186726601344 -> 2187024951280
	2184971094672 [label="bert.embeddings.position_embeddings.weight
 (512, 768)" fillcolor=lightblue]
	2184971094672 -> 2186726601344
	2186726601344 [label=AccumulateGrad]
	2187024951136 -> 2187024951088
	2187024951136 [label=EmbeddingBackward0]
	2186726601824 -> 2187024951136
	2184986307856 [label="bert.embeddings.token_type_embeddings.weight
 (2, 768)" fillcolor=lightblue]
	2184986307856 -> 2186726601824
	2186726601824 [label=AccumulateGrad]
	2186726602880 -> 2187024950992
	2184980988288 [label="bert.embeddings.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184980988288 -> 2186726602880
	2186726602880 [label=AccumulateGrad]
	2186726603024 -> 2187024950992
	2184988367584 [label="bert.embeddings.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184988367584 -> 2186726603024
	2186726603024 [label=AccumulateGrad]
	2187024950752 -> 2187024950704
	2187024950752 [label=TBackward0]
	2186726602736 -> 2187024950752
	2184971229040 [label="bert.encoder.layer.0.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184971229040 -> 2186726602736
	2186726602736 [label=AccumulateGrad]
	2187024950032 -> 2187024949984
	2187024950032 [label=UnsafeViewBackward0]
	2187024950368 -> 2187024950032
	2187024950368 [label=CloneBackward0]
	2187024950560 -> 2187024950368
	2187024950560 [label=ExpandBackward0]
	2187024950128 -> 2187024950560
	2187024950128 [label=TransposeBackward0]
	2187024950896 -> 2187024950128
	2187024950896 [label=PermuteBackward0]
	2187024950944 -> 2187024950896
	2187024950944 [label=ViewBackward0]
	2187024951424 -> 2187024950944
	2187024951424 [label=ViewBackward0]
	2187024951952 -> 2187024951424
	2187024951952 [label=AddmmBackward0]
	2186917575696 -> 2187024951952
	2184988644192 [label="bert.encoder.layer.0.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184988644192 -> 2186917575696
	2186917575696 [label=AccumulateGrad]
	2187024951664 -> 2187024951952
	2187024951664 [label=ViewBackward0]
	2187024940000 -> 2187024951664
	2187024951376 -> 2187024951952
	2187024951376 [label=TBackward0]
	2186917575504 -> 2187024951376
	2184981521808 [label="bert.encoder.layer.0.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184981521808 -> 2186917575504
	2186917575504 [label=AccumulateGrad]
	2187024940960 -> 2187024940912
	2187024940960 [label=UnsafeViewBackward0]
	2187024949552 -> 2187024940960
	2187024949552 [label=CloneBackward0]
	2187024949744 -> 2187024949552
	2187024949744 [label=ExpandBackward0]
	2187024949936 -> 2187024949744
	2187024949936 [label=PermuteBackward0]
	2187024950272 -> 2187024949936
	2187024950272 [label=ViewBackward0]
	2187024950656 -> 2187024950272
	2187024950656 [label=ViewBackward0]
	2187024950848 -> 2187024950656
	2187024950848 [label=AddmmBackward0]
	2186726602256 -> 2187024950848
	2184980961696 [label="bert.encoder.layer.0.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184980961696 -> 2186726602256
	2186726602256 [label=AccumulateGrad]
	2187024951232 -> 2187024950848
	2187024951232 [label=ViewBackward0]
	2187024940000 -> 2187024951232
	2187024952048 -> 2187024950848
	2187024952048 [label=TBackward0]
	2186917575456 -> 2187024952048
	2184971602256 [label="bert.encoder.layer.0.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184971602256 -> 2186917575456
	2186917575456 [label=AccumulateGrad]
	2187024940336 -> 2187024940288
	2187024940336 [label=TBackward0]
	2186996482352 -> 2187024940336
	2184981839600 [label="bert.encoder.layer.0.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184981839600 -> 2186996482352
	2186996482352 [label=AccumulateGrad]
	2187024940000 -> 2187024939952
	2186996483360 -> 2187024938944
	2184980238144 [label="bert.encoder.layer.0.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184980238144 -> 2186996483360
	2186996483360 [label=AccumulateGrad]
	2186996483408 -> 2187024938944
	2184985641744 [label="bert.encoder.layer.0.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184985641744 -> 2186996483408
	2186996483408 [label=AccumulateGrad]
	2187024939712 -> 2187024939664
	2187024939712 [label=TBackward0]
	2186996483216 -> 2187024939712
	2184985642464 [label="bert.encoder.layer.0.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184985642464 -> 2186996483216
	2186996483216 [label=AccumulateGrad]
	2187024939280 -> 2187024939232
	2187024939280 [label=TBackward0]
	2186996483696 -> 2187024939280
	2184989266064 [label="bert.encoder.layer.0.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184989266064 -> 2186996483696
	2186996483696 [label=AccumulateGrad]
	2187024938944 -> 2187024938896
	2186996485232 -> 2187024923808
	2184989256096 [label="bert.encoder.layer.0.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184989256096 -> 2186996485232
	2186996485232 [label=AccumulateGrad]
	2186996485328 -> 2187024923808
	2184971905040 [label="bert.encoder.layer.0.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184971905040 -> 2186996485328
	2186996485328 [label=AccumulateGrad]
	2187024938656 -> 2187024938608
	2187024938656 [label=TBackward0]
	2186996485040 -> 2187024938656
	2184980188672 [label="bert.encoder.layer.1.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184980188672 -> 2186996485040
	2186996485040 [label=AccumulateGrad]
	2187024937936 -> 2187024937888
	2187024937936 [label=UnsafeViewBackward0]
	2187024938272 -> 2187024937936
	2187024938272 [label=CloneBackward0]
	2187024938464 -> 2187024938272
	2187024938464 [label=ExpandBackward0]
	2187024938032 -> 2187024938464
	2187024938032 [label=TransposeBackward0]
	2187024939088 -> 2187024938032
	2187024939088 [label=PermuteBackward0]
	2187024938848 -> 2187024939088
	2187024938848 [label=ViewBackward0]
	2187024939040 -> 2187024938848
	2187024939040 [label=ViewBackward0]
	2187024939472 -> 2187024939040
	2187024939472 [label=AddmmBackward0]
	2186996482928 -> 2187024939472
	2184985208928 [label="bert.encoder.layer.1.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184985208928 -> 2186996482928
	2186996482928 [label=AccumulateGrad]
	2187024939520 -> 2187024939472
	2187024939520 [label=ViewBackward0]
	2187024923808 -> 2187024939520
	2187024939424 -> 2187024939472
	2187024939424 [label=TBackward0]
	2186996482496 -> 2187024939424
	2184985207408 [label="bert.encoder.layer.1.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184985207408 -> 2186996482496
	2186996482496 [label=AccumulateGrad]
	2187024937120 -> 2187024924336
	2187024937120 [label=UnsafeViewBackward0]
	2187024937456 -> 2187024937120
	2187024937456 [label=CloneBackward0]
	2187024937648 -> 2187024937456
	2187024937648 [label=ExpandBackward0]
	2187024937840 -> 2187024937648
	2187024937840 [label=PermuteBackward0]
	2187024938176 -> 2187024937840
	2187024938176 [label=ViewBackward0]
	2187024938560 -> 2187024938176
	2187024938560 [label=ViewBackward0]
	2187024938752 -> 2187024938560
	2187024938752 [label=AddmmBackward0]
	2186996483264 -> 2187024938752
	2184985671616 [label="bert.encoder.layer.1.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184985671616 -> 2186996483264
	2186996483264 [label=AccumulateGrad]
	2187024939616 -> 2187024938752
	2187024939616 [label=ViewBackward0]
	2187024923808 -> 2187024939616
	2187024939184 -> 2187024938752
	2187024939184 [label=TBackward0]
	2186996482208 -> 2187024939184
	2184985206928 [label="bert.encoder.layer.1.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184985206928 -> 2186996482208
	2186996482208 [label=AccumulateGrad]
	2187024924144 -> 2187024924096
	2187024924144 [label=TBackward0]
	2186996495888 -> 2187024924144
	2184890577520 [label="bert.encoder.layer.1.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184890577520 -> 2186996495888
	2186996495888 [label=AccumulateGrad]
	2187024923808 -> 2187024923760
	2186996376192 -> 2187024922752
	2184890577280 [label="bert.encoder.layer.1.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184890577280 -> 2186996376192
	2186996376192 [label=AccumulateGrad]
	2186996376240 -> 2187024922752
	2184890577360 [label="bert.encoder.layer.1.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184890577360 -> 2186996376240
	2186996376240 [label=AccumulateGrad]
	2187024923520 -> 2187024923472
	2187024923520 [label=TBackward0]
	2186996375904 -> 2187024923520
	2184890577920 [label="bert.encoder.layer.1.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184890577920 -> 2186996375904
	2186996375904 [label=AccumulateGrad]
	2187024923088 -> 2187024923040
	2187024923088 [label=TBackward0]
	2186996376720 -> 2187024923088
	2184890646336 [label="bert.encoder.layer.1.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184890646336 -> 2186996376720
	2186996376720 [label=AccumulateGrad]
	2187024922752 -> 2187024922704
	2186996377776 -> 2187024911712
	2184890645856 [label="bert.encoder.layer.1.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184890645856 -> 2186996377776
	2186996377776 [label=AccumulateGrad]
	2186996377824 -> 2187024911712
	2184890646016 [label="bert.encoder.layer.1.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184890646016 -> 2186996377824
	2186996377824 [label=AccumulateGrad]
	2187024922464 -> 2187024922416
	2187024922464 [label=TBackward0]
	2186996377632 -> 2187024922464
	2184890645696 [label="bert.encoder.layer.2.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184890645696 -> 2186996377632
	2186996377632 [label=AccumulateGrad]
	2187024921744 -> 2187024921696
	2187024921744 [label=UnsafeViewBackward0]
	2187024922080 -> 2187024921744
	2187024922080 [label=CloneBackward0]
	2187024922272 -> 2187024922080
	2187024922272 [label=ExpandBackward0]
	2187024921840 -> 2187024922272
	2187024921840 [label=TransposeBackward0]
	2187024922896 -> 2187024921840
	2187024922896 [label=PermuteBackward0]
	2187024922656 -> 2187024922896
	2187024922656 [label=ViewBackward0]
	2187024922848 -> 2187024922656
	2187024922848 [label=ViewBackward0]
	2187024923280 -> 2187024922848
	2187024923280 [label=AddmmBackward0]
	2186996376288 -> 2187024923280
	2184890645216 [label="bert.encoder.layer.2.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184890645216 -> 2186996376288
	2186996376288 [label=AccumulateGrad]
	2187024923328 -> 2187024923280
	2187024923328 [label=ViewBackward0]
	2187024911712 -> 2187024923328
	2187024923232 -> 2187024923280
	2187024923232 [label=TBackward0]
	2186996496032 -> 2187024923232
	2184890645376 [label="bert.encoder.layer.2.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184890645376 -> 2186996496032
	2186996496032 [label=AccumulateGrad]
	2187024920928 -> 2187024920880
	2187024920928 [label=UnsafeViewBackward0]
	2187024921264 -> 2187024920928
	2187024921264 [label=CloneBackward0]
	2187024921456 -> 2187024921264
	2187024921456 [label=ExpandBackward0]
	2187024921648 -> 2187024921456
	2187024921648 [label=PermuteBackward0]
	2187024921984 -> 2187024921648
	2187024921984 [label=ViewBackward0]
	2187024922368 -> 2187024921984
	2187024922368 [label=ViewBackward0]
	2187024922560 -> 2187024922368
	2187024922560 [label=AddmmBackward0]
	2186996376000 -> 2187024922560
	2184890644896 [label="bert.encoder.layer.2.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184890644896 -> 2186996376000
	2186996376000 [label=AccumulateGrad]
	2187024923424 -> 2187024922560
	2187024923424 [label=ViewBackward0]
	2187024911712 -> 2187024923424
	2187024922992 -> 2187024922560
	2187024922992 [label=TBackward0]
	2186996495696 -> 2187024922992
	2184890645056 [label="bert.encoder.layer.2.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184890645056 -> 2186996495696
	2186996495696 [label=AccumulateGrad]
	2187024912048 -> 2187024912000
	2187024912048 [label=TBackward0]
	2186996388912 -> 2187024912048
	2184890644736 [label="bert.encoder.layer.2.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184890644736 -> 2186996388912
	2186996388912 [label=AccumulateGrad]
	2187024911712 -> 2187024911664
	2186996389968 -> 2187024910656
	2184890644416 [label="bert.encoder.layer.2.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184890644416 -> 2186996389968
	2186996389968 [label=AccumulateGrad]
	2186996390064 -> 2187024910656
	2184890644256 [label="bert.encoder.layer.2.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184890644256 -> 2186996390064
	2186996390064 [label=AccumulateGrad]
	2187024911424 -> 2187024911376
	2187024911424 [label=TBackward0]
	2186996389824 -> 2187024911424
	2184971230640 [label="bert.encoder.layer.2.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184971230640 -> 2186996389824
	2186996389824 [label=AccumulateGrad]
	2187024910992 -> 2187024910944
	2187024910992 [label=TBackward0]
	2186996390352 -> 2187024910992
	2184890643936 [label="bert.encoder.layer.2.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184890643936 -> 2186996390352
	2186996390352 [label=AccumulateGrad]
	2187024910656 -> 2187024910608
	2186996391552 -> 2187024899616
	2184890643776 [label="bert.encoder.layer.2.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184890643776 -> 2186996391552
	2186996391552 [label=AccumulateGrad]
	2186996391600 -> 2187024899616
	2184890643456 [label="bert.encoder.layer.2.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184890643456 -> 2186996391600
	2186996391600 [label=AccumulateGrad]
	2187024910368 -> 2187024910320
	2187024910368 [label=TBackward0]
	2186996391264 -> 2187024910368
	2184890643296 [label="bert.encoder.layer.3.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184890643296 -> 2186996391264
	2186996391264 [label=AccumulateGrad]
	2187024909648 -> 2187024909600
	2187024909648 [label=UnsafeViewBackward0]
	2187024909984 -> 2187024909648
	2187024909984 [label=CloneBackward0]
	2187024910176 -> 2187024909984
	2187024910176 [label=ExpandBackward0]
	2187024909744 -> 2187024910176
	2187024909744 [label=TransposeBackward0]
	2187024910800 -> 2187024909744
	2187024910800 [label=PermuteBackward0]
	2187024910560 -> 2187024910800
	2187024910560 [label=ViewBackward0]
	2187024910752 -> 2187024910560
	2187024910752 [label=ViewBackward0]
	2187024911184 -> 2187024910752
	2187024911184 [label=AddmmBackward0]
	2186996389488 -> 2187024911184
	2184890642656 [label="bert.encoder.layer.3.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184890642656 -> 2186996389488
	2186996389488 [label=AccumulateGrad]
	2187024911232 -> 2187024911184
	2187024911232 [label=ViewBackward0]
	2187024899616 -> 2187024911232
	2187024911136 -> 2187024911184
	2187024911136 [label=TBackward0]
	2186996389056 -> 2187024911136
	2184890642976 [label="bert.encoder.layer.3.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184890642976 -> 2186996389056
	2186996389056 [label=AccumulateGrad]
	2187024908832 -> 2187024908784
	2187024908832 [label=UnsafeViewBackward0]
	2187024909168 -> 2187024908832
	2187024909168 [label=CloneBackward0]
	2187024909360 -> 2187024909168
	2187024909360 [label=ExpandBackward0]
	2187024909552 -> 2187024909360
	2187024909552 [label=PermuteBackward0]
	2187024909888 -> 2187024909552
	2187024909888 [label=ViewBackward0]
	2187024910272 -> 2187024909888
	2187024910272 [label=ViewBackward0]
	2187024910464 -> 2187024910272
	2187024910464 [label=AddmmBackward0]
	2186996389872 -> 2187024910464
	2184890642816 [label="bert.encoder.layer.3.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184890642816 -> 2186996389872
	2186996389872 [label=AccumulateGrad]
	2187024911328 -> 2187024910464
	2187024911328 [label=ViewBackward0]
	2187024899616 -> 2187024911328
	2187024910896 -> 2187024910464
	2187024910896 [label=TBackward0]
	2186996388672 -> 2187024910896
	2184890642496 [label="bert.encoder.layer.3.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184890642496 -> 2186996388672
	2186996388672 [label=AccumulateGrad]
	2187024899952 -> 2187024899904
	2187024899952 [label=TBackward0]
	2186996402544 -> 2187024899952
	2184890640240 [label="bert.encoder.layer.3.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184890640240 -> 2186996402544
	2186996402544 [label=AccumulateGrad]
	2187024899616 -> 2187024899568
	2186996403744 -> 2187024898560
	2184890639920 [label="bert.encoder.layer.3.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184890639920 -> 2186996403744
	2186996403744 [label=AccumulateGrad]
	2186996403936 -> 2187024898560
	2184890639760 [label="bert.encoder.layer.3.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184890639760 -> 2186996403936
	2186996403936 [label=AccumulateGrad]
	2187024899328 -> 2187024899280
	2187024899328 [label=TBackward0]
	2186996403600 -> 2187024899328
	2184890639600 [label="bert.encoder.layer.3.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184890639600 -> 2186996403600
	2186996403600 [label=AccumulateGrad]
	2187024898896 -> 2187024898848
	2187024898896 [label=TBackward0]
	2186996416816 -> 2187024898896
	2184890639280 [label="bert.encoder.layer.3.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184890639280 -> 2186996416816
	2186996416816 [label=AccumulateGrad]
	2187024898560 -> 2187024898512
	2186996417968 -> 2187024887520
	2184890639120 [label="bert.encoder.layer.3.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184890639120 -> 2186996417968
	2186996417968 [label=AccumulateGrad]
	2186996418016 -> 2187024887520
	2184890638800 [label="bert.encoder.layer.3.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184890638800 -> 2186996418016
	2186996418016 [label=AccumulateGrad]
	2187024898272 -> 2187024898224
	2187024898272 [label=TBackward0]
	2186996417776 -> 2187024898272
	2184890638640 [label="bert.encoder.layer.4.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184890638640 -> 2186996417776
	2186996417776 [label=AccumulateGrad]
	2187024897552 -> 2187024897504
	2187024897552 [label=UnsafeViewBackward0]
	2187024897888 -> 2187024897552
	2187024897888 [label=CloneBackward0]
	2187024898080 -> 2187024897888
	2187024898080 [label=ExpandBackward0]
	2187024897648 -> 2187024898080
	2187024897648 [label=TransposeBackward0]
	2187024898704 -> 2187024897648
	2187024898704 [label=PermuteBackward0]
	2187024898464 -> 2187024898704
	2187024898464 [label=ViewBackward0]
	2187024898656 -> 2187024898464
	2187024898656 [label=ViewBackward0]
	2187024899088 -> 2187024898656
	2187024899088 [label=AddmmBackward0]
	2186996403024 -> 2187024899088
	2184890642080 [label="bert.encoder.layer.4.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184890642080 -> 2186996403024
	2186996403024 [label=AccumulateGrad]
	2187024899136 -> 2187024899088
	2187024899136 [label=ViewBackward0]
	2187024887520 -> 2187024899136
	2187024899040 -> 2187024899088
	2187024899040 [label=TBackward0]
	2186996402640 -> 2187024899040
	2184890642240 [label="bert.encoder.layer.4.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184890642240 -> 2186996402640
	2186996402640 [label=AccumulateGrad]
	2187024896736 -> 2187024896688
	2187024896736 [label=UnsafeViewBackward0]
	2187024897072 -> 2187024896736
	2187024897072 [label=CloneBackward0]
	2187024897264 -> 2187024897072
	2187024897264 [label=ExpandBackward0]
	2187024897456 -> 2187024897264
	2187024897456 [label=PermuteBackward0]
	2187024897792 -> 2187024897456
	2187024897792 [label=ViewBackward0]
	2187024898176 -> 2187024897792
	2187024898176 [label=ViewBackward0]
	2187024898368 -> 2187024898176
	2187024898368 [label=AddmmBackward0]
	2186996418832 -> 2187024898368
	2184890641840 [label="bert.encoder.layer.4.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184890641840 -> 2186996418832
	2186996418832 [label=AccumulateGrad]
	2187024899232 -> 2187024898368
	2187024899232 [label=ViewBackward0]
	2187024887520 -> 2187024899232
	2187024898800 -> 2187024898368
	2187024898800 [label=TBackward0]
	2186996402352 -> 2187024898800
	2184890641920 [label="bert.encoder.layer.4.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184890641920 -> 2186996402352
	2186996402352 [label=AccumulateGrad]
	2187024896112 -> 2187024887760
	2187024896112 [label=TBackward0]
	2186996429056 -> 2187024896112
	2184890640400 [label="bert.encoder.layer.4.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184890640400 -> 2186996429056
	2186996429056 [label=AccumulateGrad]
	2187024887520 -> 2187024887472
	2186996430160 -> 2187024886464
	2184890629792 [label="bert.encoder.layer.4.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184890629792 -> 2186996430160
	2186996430160 [label=AccumulateGrad]
	2186996430208 -> 2187024886464
	2184890629632 [label="bert.encoder.layer.4.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184890629632 -> 2186996430208
	2186996430208 [label=AccumulateGrad]
	2187024887232 -> 2187024887184
	2187024887232 [label=TBackward0]
	2186996430016 -> 2187024887232
	2184890629472 [label="bert.encoder.layer.4.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184890629472 -> 2186996430016
	2186996430016 [label=AccumulateGrad]
	2187024886800 -> 2187024886752
	2187024886800 [label=TBackward0]
	2186996430544 -> 2187024886800
	2184890629152 [label="bert.encoder.layer.4.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184890629152 -> 2186996430544
	2186996430544 [label=AccumulateGrad]
	2187024886464 -> 2187024886416
	2186996431600 -> 2187024871328
	2184890628832 [label="bert.encoder.layer.4.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184890628832 -> 2186996431600
	2186996431600 [label=AccumulateGrad]
	2186996431648 -> 2187024871328
	2184890628512 [label="bert.encoder.layer.4.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184890628512 -> 2186996431648
	2186996431648 [label=AccumulateGrad]
	2187024886176 -> 2187024886128
	2187024886176 [label=TBackward0]
	2186996431312 -> 2187024886176
	2184990769616 [label="bert.encoder.layer.5.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184990769616 -> 2186996431312
	2186996431312 [label=AccumulateGrad]
	2187024885456 -> 2187024885408
	2187024885456 [label=UnsafeViewBackward0]
	2187024885792 -> 2187024885456
	2187024885792 [label=CloneBackward0]
	2187024885984 -> 2187024885792
	2187024885984 [label=ExpandBackward0]
	2187024885552 -> 2187024885984
	2187024885552 [label=TransposeBackward0]
	2187024886608 -> 2187024885552
	2187024886608 [label=PermuteBackward0]
	2187024886368 -> 2187024886608
	2187024886368 [label=ViewBackward0]
	2187024886560 -> 2187024886368
	2187024886560 [label=ViewBackward0]
	2187024886992 -> 2187024886560
	2187024886992 [label=AddmmBackward0]
	2186996429632 -> 2187024886992
	2184990752416 [label="bert.encoder.layer.5.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184990752416 -> 2186996429632
	2186996429632 [label=AccumulateGrad]
	2187024887040 -> 2187024886992
	2187024887040 [label=ViewBackward0]
	2187024871328 -> 2187024887040
	2187024886944 -> 2187024886992
	2187024886944 [label=TBackward0]
	2186996429200 -> 2187024886944
	2184990752656 [label="bert.encoder.layer.5.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184990752656 -> 2186996429200
	2186996429200 [label=AccumulateGrad]
	2187024884640 -> 2187024884592
	2187024884640 [label=UnsafeViewBackward0]
	2187024884976 -> 2187024884640
	2187024884976 [label=CloneBackward0]
	2187024885168 -> 2187024884976
	2187024885168 [label=ExpandBackward0]
	2187024885360 -> 2187024885168
	2187024885360 [label=PermuteBackward0]
	2187024885696 -> 2187024885360
	2187024885696 [label=ViewBackward0]
	2187024886080 -> 2187024885696
	2187024886080 [label=ViewBackward0]
	2187024886272 -> 2187024886080
	2187024886272 [label=AddmmBackward0]
	2186996430064 -> 2187024886272
	2184990751936 [label="bert.encoder.layer.5.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184990751936 -> 2186996430064
	2186996430064 [label=AccumulateGrad]
	2187024887136 -> 2187024886272
	2187024887136 [label=ViewBackward0]
	2187024871328 -> 2187024887136
	2187024886704 -> 2187024886272
	2187024886704 [label=TBackward0]
	2186996430256 -> 2187024886704
	2184990752176 [label="bert.encoder.layer.5.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184990752176 -> 2186996430256
	2186996430256 [label=AccumulateGrad]
	2187024884016 -> 2187024883968
	2187024884016 [label=TBackward0]
	2186996442736 -> 2187024884016
	2184990751696 [label="bert.encoder.layer.5.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184990751696 -> 2186996442736
	2186996442736 [label=AccumulateGrad]
	2187024871328 -> 2187024871280
	2186996443888 -> 2187024870272
	2184990751296 [label="bert.encoder.layer.5.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990751296 -> 2186996443888
	2186996443888 [label=AccumulateGrad]
	2186996443984 -> 2187024870272
	2184990750976 [label="bert.encoder.layer.5.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990750976 -> 2186996443984
	2186996443984 [label=AccumulateGrad]
	2187024871040 -> 2187024870992
	2187024871040 [label=TBackward0]
	2186996443648 -> 2187024871040
	2184990750736 [label="bert.encoder.layer.5.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184990750736 -> 2186996443648
	2186996443648 [label=AccumulateGrad]
	2187024870608 -> 2187024870560
	2187024870608 [label=TBackward0]
	2186996444512 -> 2187024870608
	2184990750256 [label="bert.encoder.layer.5.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184990750256 -> 2186996444512
	2186996444512 [label=AccumulateGrad]
	2187024870272 -> 2187024870224
	2186996454016 -> 2187024867488
	2184990749696 [label="bert.encoder.layer.5.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990749696 -> 2186996454016
	2186996454016 [label=AccumulateGrad]
	2186996454064 -> 2187024867488
	2184990749376 [label="bert.encoder.layer.5.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990749376 -> 2186996454064
	2186996454064 [label=AccumulateGrad]
	2187024869984 -> 2187024869936
	2187024869984 [label=TBackward0]
	2186996453824 -> 2187024869984
	2184990749056 [label="bert.encoder.layer.6.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184990749056 -> 2186996453824
	2186996453824 [label=AccumulateGrad]
	2187024869264 -> 2187024869216
	2187024869264 [label=UnsafeViewBackward0]
	2187024869600 -> 2187024869264
	2187024869600 [label=CloneBackward0]
	2187024869792 -> 2187024869600
	2187024869792 [label=ExpandBackward0]
	2187024869360 -> 2187024869792
	2187024869360 [label=TransposeBackward0]
	2187024870416 -> 2187024869360
	2187024870416 [label=PermuteBackward0]
	2187024870176 -> 2187024870416
	2187024870176 [label=ViewBackward0]
	2187024870368 -> 2187024870176
	2187024870368 [label=ViewBackward0]
	2187024870800 -> 2187024870368
	2187024870800 [label=AddmmBackward0]
	2186996443216 -> 2187024870800
	2184990735952 [label="bert.encoder.layer.6.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184990735952 -> 2186996443216
	2186996443216 [label=AccumulateGrad]
	2187024870848 -> 2187024870800
	2187024870848 [label=ViewBackward0]
	2187024867488 -> 2187024870848
	2187024870752 -> 2187024870800
	2187024870752 [label=TBackward0]
	2186996442832 -> 2187024870752
	2184990736192 [label="bert.encoder.layer.6.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184990736192 -> 2186996442832
	2186996442832 [label=AccumulateGrad]
	2187024868448 -> 2187024868400
	2187024868448 [label=UnsafeViewBackward0]
	2187024868784 -> 2187024868448
	2187024868784 [label=CloneBackward0]
	2187024868976 -> 2187024868784
	2187024868976 [label=ExpandBackward0]
	2187024869168 -> 2187024868976
	2187024869168 [label=PermuteBackward0]
	2187024869504 -> 2187024869168
	2187024869504 [label=ViewBackward0]
	2187024869888 -> 2187024869504
	2187024869888 [label=ViewBackward0]
	2187024870080 -> 2187024869888
	2187024870080 [label=AddmmBackward0]
	2186996455984 -> 2187024870080
	2184990735472 [label="bert.encoder.layer.6.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184990735472 -> 2186996455984
	2186996455984 [label=AccumulateGrad]
	2187024870944 -> 2187024870080
	2187024870944 [label=ViewBackward0]
	2187024867488 -> 2187024870944
	2187024870512 -> 2187024870080
	2187024870512 [label=TBackward0]
	2186996442544 -> 2187024870512
	2184990735712 [label="bert.encoder.layer.6.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184990735712 -> 2186996442544
	2186996442544 [label=AccumulateGrad]
	2187024867824 -> 2187024867776
	2187024867824 [label=TBackward0]
	2186996456752 -> 2187024867824
	2184990735232 [label="bert.encoder.layer.6.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184990735232 -> 2186996456752
	2186996456752 [label=AccumulateGrad]
	2187024867488 -> 2187024859088
	2186996470304 -> 2187024858176
	2184990734752 [label="bert.encoder.layer.6.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990734752 -> 2186996470304
	2186996470304 [label=AccumulateGrad]
	2186996470400 -> 2187024858176
	2184990734512 [label="bert.encoder.layer.6.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990734512 -> 2186996470400
	2186996470400 [label=AccumulateGrad]
	2187024858944 -> 2187024858896
	2187024858944 [label=TBackward0]
	2186996470160 -> 2187024858944
	2184990734272 [label="bert.encoder.layer.6.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184990734272 -> 2186996470160
	2186996470160 [label=AccumulateGrad]
	2187024858512 -> 2187024858464
	2187024858512 [label=TBackward0]
	2186996470688 -> 2187024858512
	2184990733792 [label="bert.encoder.layer.6.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184990733792 -> 2186996470688
	2186996470688 [label=AccumulateGrad]
	2187024858176 -> 2187024858128
	2186996471648 -> 2187024855392
	2184990733312 [label="bert.encoder.layer.6.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990733312 -> 2186996471648
	2186996471648 [label=AccumulateGrad]
	2186996471696 -> 2187024855392
	2184990733072 [label="bert.encoder.layer.6.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990733072 -> 2186996471696
	2186996471696 [label=AccumulateGrad]
	2187024857888 -> 2187024857840
	2187024857888 [label=TBackward0]
	2186996471504 -> 2187024857888
	2184990732832 [label="bert.encoder.layer.7.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184990732832 -> 2186996471504
	2186996471504 [label=AccumulateGrad]
	2187024857168 -> 2187024857120
	2187024857168 [label=UnsafeViewBackward0]
	2187024857504 -> 2187024857168
	2187024857504 [label=CloneBackward0]
	2187024857696 -> 2187024857504
	2187024857696 [label=ExpandBackward0]
	2187024857264 -> 2187024857696
	2187024857264 [label=TransposeBackward0]
	2187024858320 -> 2187024857264
	2187024858320 [label=PermuteBackward0]
	2187024858080 -> 2187024858320
	2187024858080 [label=ViewBackward0]
	2187024858272 -> 2187024858080
	2187024858272 [label=ViewBackward0]
	2187024858704 -> 2187024858272
	2187024858704 [label=AddmmBackward0]
	2186996469968 -> 2187024858704
	2184990715632 [label="bert.encoder.layer.7.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184990715632 -> 2186996469968
	2186996469968 [label=AccumulateGrad]
	2187024858752 -> 2187024858704
	2187024858752 [label=ViewBackward0]
	2187024855392 -> 2187024858752
	2187024858656 -> 2187024858704
	2187024858656 [label=TBackward0]
	2186996456848 -> 2187024858656
	2184990732352 [label="bert.encoder.layer.7.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184990732352 -> 2186996456848
	2186996456848 [label=AccumulateGrad]
	2187024856352 -> 2187024856304
	2187024856352 [label=UnsafeViewBackward0]
	2187024856688 -> 2187024856352
	2187024856688 [label=CloneBackward0]
	2187024856880 -> 2187024856688
	2187024856880 [label=ExpandBackward0]
	2187024857072 -> 2187024856880
	2187024857072 [label=PermuteBackward0]
	2187024857408 -> 2187024857072
	2187024857408 [label=ViewBackward0]
	2187024857792 -> 2187024857408
	2187024857792 [label=ViewBackward0]
	2187024857984 -> 2187024857792
	2187024857984 [label=AddmmBackward0]
	2186996473808 -> 2187024857984
	2184990715152 [label="bert.encoder.layer.7.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184990715152 -> 2186996473808
	2186996473808 [label=AccumulateGrad]
	2187024858848 -> 2187024857984
	2187024858848 [label=ViewBackward0]
	2187024855392 -> 2187024858848
	2187024858416 -> 2187024857984
	2187024858416 [label=TBackward0]
	2186996456944 -> 2187024858416
	2184990715392 [label="bert.encoder.layer.7.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184990715392 -> 2186996456944
	2186996456944 [label=AccumulateGrad]
	2187024855728 -> 2187024855680
	2187024855728 [label=TBackward0]
	2186996735808 -> 2187024855728
	2184990714912 [label="bert.encoder.layer.7.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184990714912 -> 2186996735808
	2186996735808 [label=AccumulateGrad]
	2187024855392 -> 2187024855344
	2186474678928 -> 2187024846080
	2184990714432 [label="bert.encoder.layer.7.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990714432 -> 2186474678928
	2186474678928 [label=AccumulateGrad]
	2186474678880 -> 2187024846080
	2184990714192 [label="bert.encoder.layer.7.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990714192 -> 2186474678880
	2186474678880 [label=AccumulateGrad]
	2187024855152 -> 2187024846800
	2187024855152 [label=TBackward0]
	2186996734992 -> 2187024855152
	2184990713952 [label="bert.encoder.layer.7.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184990713952 -> 2186996734992
	2186996734992 [label=AccumulateGrad]
	2187024846416 -> 2187024846368
	2187024846416 [label=TBackward0]
	2186996734752 -> 2187024846416
	2184990713472 [label="bert.encoder.layer.7.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184990713472 -> 2186996734752
	2186996734752 [label=AccumulateGrad]
	2187024846080 -> 2187024846032
	2186474677392 -> 2187024843296
	2184990712992 [label="bert.encoder.layer.7.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990712992 -> 2186474677392
	2186474677392 [label=AccumulateGrad]
	2186474677344 -> 2187024843296
	2184990712752 [label="bert.encoder.layer.7.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990712752 -> 2186474677344
	2186474677344 [label=AccumulateGrad]
	2187024845792 -> 2187024845744
	2187024845792 [label=TBackward0]
	2186996733936 -> 2187024845792
	2184990712512 [label="bert.encoder.layer.8.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184990712512 -> 2186996733936
	2186996733936 [label=AccumulateGrad]
	2187024845072 -> 2187024845024
	2187024845072 [label=UnsafeViewBackward0]
	2187024845408 -> 2187024845072
	2187024845408 [label=CloneBackward0]
	2187024845600 -> 2187024845408
	2187024845600 [label=ExpandBackward0]
	2187024845168 -> 2187024845600
	2187024845168 [label=TransposeBackward0]
	2187024846224 -> 2187024845168
	2187024846224 [label=PermuteBackward0]
	2187024845984 -> 2187024846224
	2187024845984 [label=ViewBackward0]
	2187024846176 -> 2187024845984
	2187024846176 [label=ViewBackward0]
	2187024846560 -> 2187024846176
	2187024846560 [label=AddmmBackward0]
	2186996735376 -> 2187024846560
	2184990695312 [label="bert.encoder.layer.8.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184990695312 -> 2186996735376
	2186996735376 [label=AccumulateGrad]
	2187024846656 -> 2187024846560
	2187024846656 [label=ViewBackward0]
	2187024843296 -> 2187024846656
	2187024845216 -> 2187024846560
	2187024845216 [label=TBackward0]
	2186996735712 -> 2187024845216
	2184990712032 [label="bert.encoder.layer.8.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184990712032 -> 2186996735712
	2186996735712 [label=AccumulateGrad]
	2187024844256 -> 2187024844208
	2187024844256 [label=UnsafeViewBackward0]
	2187024844592 -> 2187024844256
	2187024844592 [label=CloneBackward0]
	2187024844784 -> 2187024844592
	2187024844784 [label=ExpandBackward0]
	2187024844976 -> 2187024844784
	2187024844976 [label=PermuteBackward0]
	2187024845312 -> 2187024844976
	2187024845312 [label=ViewBackward0]
	2187024845696 -> 2187024845312
	2187024845696 [label=ViewBackward0]
	2187024845888 -> 2187024845696
	2187024845888 [label=AddmmBackward0]
	2186996735040 -> 2187024845888
	2184990694832 [label="bert.encoder.layer.8.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184990694832 -> 2186996735040
	2186996735040 [label=AccumulateGrad]
	2187024846752 -> 2187024845888
	2187024846752 [label=ViewBackward0]
	2187024843296 -> 2187024846752
	2187024846320 -> 2187024845888
	2187024846320 [label=TBackward0]
	2186996735904 -> 2187024846320
	2184990695072 [label="bert.encoder.layer.8.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184990695072 -> 2186996735904
	2186996735904 [label=AccumulateGrad]
	2187024843632 -> 2187024843584
	2187024843632 [label=TBackward0]
	2186996723616 -> 2187024843632
	2184990694592 [label="bert.encoder.layer.8.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184990694592 -> 2186996723616
	2186996723616 [label=AccumulateGrad]
	2187024843296 -> 2187024843248
	2186474665248 -> 2187268181440
	2184990694112 [label="bert.encoder.layer.8.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990694112 -> 2186474665248
	2186474665248 [label=AccumulateGrad]
	2186474665200 -> 2187268181440
	2184990693872 [label="bert.encoder.layer.8.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990693872 -> 2186474665200
	2186474665200 [label=AccumulateGrad]
	2187024843008 -> 2187024842960
	2187024843008 [label=TBackward0]
	2186996722896 -> 2187024843008
	2184990693632 [label="bert.encoder.layer.8.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184990693632 -> 2186996722896
	2186996722896 [label=AccumulateGrad]
	2187268181776 -> 2187268181728
	2187268181776 [label=TBackward0]
	2186996722656 -> 2187268181776
	2184990692912 [label="bert.encoder.layer.8.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184990692912 -> 2186996722656
	2186996722656 [label=AccumulateGrad]
	2187268181440 -> 2187268181392
	2186474659552 -> 2187268169104
	2184990692432 [label="bert.encoder.layer.8.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990692432 -> 2186474659552
	2186474659552 [label=AccumulateGrad]
	2186474659504 -> 2187268169104
	2184990692192 [label="bert.encoder.layer.8.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990692192 -> 2186474659504
	2186474659504 [label=AccumulateGrad]
	2187268181152 -> 2187268181104
	2187268181152 [label=TBackward0]
	2186996721840 -> 2187268181152
	2184990691952 [label="bert.encoder.layer.9.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184990691952 -> 2186996721840
	2186996721840 [label=AccumulateGrad]
	2187268180432 -> 2187268180384
	2187268180432 [label=UnsafeViewBackward0]
	2187268180768 -> 2187268180432
	2187268180768 [label=CloneBackward0]
	2187268180960 -> 2187268180768
	2187268180960 [label=ExpandBackward0]
	2187268180528 -> 2187268180960
	2187268180528 [label=TransposeBackward0]
	2187268181584 -> 2187268180528
	2187268181584 [label=PermuteBackward0]
	2187268181344 -> 2187268181584
	2187268181344 [label=ViewBackward0]
	2187268181536 -> 2187268181344
	2187268181536 [label=ViewBackward0]
	2187268181920 -> 2187268181536
	2187268181920 [label=AddmmBackward0]
	2186996723280 -> 2187268181920
	2184990674752 [label="bert.encoder.layer.9.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184990674752 -> 2186996723280
	2186996723280 [label=AccumulateGrad]
	2187024842912 -> 2187268181920
	2187024842912 [label=ViewBackward0]
	2187268169104 -> 2187024842912
	2187024842816 -> 2187268181920
	2187024842816 [label=TBackward0]
	2186996723520 -> 2187024842816
	2184990691472 [label="bert.encoder.layer.9.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184990691472 -> 2186996723520
	2186996723520 [label=AccumulateGrad]
	2187268179616 -> 2187268179568
	2187268179616 [label=UnsafeViewBackward0]
	2187268179952 -> 2187268179616
	2187268179952 [label=CloneBackward0]
	2187268180144 -> 2187268179952
	2187268180144 [label=ExpandBackward0]
	2187268180336 -> 2187268180144
	2187268180336 [label=PermuteBackward0]
	2187268180672 -> 2187268180336
	2187268180672 [label=ViewBackward0]
	2187268181056 -> 2187268180672
	2187268181056 [label=ViewBackward0]
	2187268181248 -> 2187268181056
	2187268181248 [label=AddmmBackward0]
	2186996722944 -> 2187268181248
	2184990674272 [label="bert.encoder.layer.9.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184990674272 -> 2186996722944
	2186996722944 [label=AccumulateGrad]
	2187268180576 -> 2187268181248
	2187268180576 [label=ViewBackward0]
	2187268169104 -> 2187268180576
	2187268181680 -> 2187268181248
	2187268181680 [label=TBackward0]
	2186996722848 -> 2187268181680
	2184990674512 [label="bert.encoder.layer.9.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184990674512 -> 2186996722848
	2186996722848 [label=AccumulateGrad]
	2187268178320 -> 2187268178944
	2187268178320 [label=TBackward0]
	2186996719872 -> 2187268178320
	2184990674032 [label="bert.encoder.layer.9.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184990674032 -> 2186996719872
	2186996719872 [label=AccumulateGrad]
	2187268169104 -> 2187268168528
	2186474643312 -> 2187268155280
	2184990673552 [label="bert.encoder.layer.9.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990673552 -> 2186474643312
	2186474643312 [label=AccumulateGrad]
	2186474643264 -> 2187268155280
	2184990673312 [label="bert.encoder.layer.9.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990673312 -> 2186474643264
	2186474643264 [label=AccumulateGrad]
	2187268166848 -> 2187268167184
	2187268166848 [label=TBackward0]
	2186996719968 -> 2187268166848
	2184990673072 [label="bert.encoder.layer.9.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184990673072 -> 2186996719968
	2186996719968 [label=AccumulateGrad]
	2187268166128 -> 2187268165936
	2187268166128 [label=TBackward0]
	2186996719680 -> 2187268166128
	2184990672592 [label="bert.encoder.layer.9.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184990672592 -> 2186996719680
	2186996719680 [label=AccumulateGrad]
	2187268155280 -> 2187268155808
	2186474641776 -> 2187268443104
	2184990672112 [label="bert.encoder.layer.9.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990672112 -> 2186474641776
	2186474641776 [label=AccumulateGrad]
	2186474641728 -> 2187268443104
	2184990671872 [label="bert.encoder.layer.9.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990671872 -> 2186474641728
	2186474641728 [label=AccumulateGrad]
	2187268154800 -> 2187268154704
	2187268154800 [label=TBackward0]
	2186996719776 -> 2187268154800
	2184990671632 [label="bert.encoder.layer.10.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184990671632 -> 2186996719776
	2186996719776 [label=AccumulateGrad]
	2187268139616 -> 2187268139424
	2187268139616 [label=UnsafeViewBackward0]
	2187268140240 -> 2187268139616
	2187268140240 [label=CloneBackward0]
	2187268154320 -> 2187268140240
	2187268154320 [label=ExpandBackward0]
	2187268153408 -> 2187268154320
	2187268153408 [label=TransposeBackward0]
	2187268154752 -> 2187268153408
	2187268154752 [label=PermuteBackward0]
	2187268155616 -> 2187268154752
	2187268155616 [label=ViewBackward0]
	2187268165792 -> 2187268155616
	2187268165792 [label=ViewBackward0]
	2187268166992 -> 2187268165792
	2187268166992 [label=AddmmBackward0]
	2186996732880 -> 2187268166992
	2184990670912 [label="bert.encoder.layer.10.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184990670912 -> 2186996732880
	2186996732880 [label=AccumulateGrad]
	2187268167088 -> 2187268166992
	2187268167088 [label=ViewBackward0]
	2187268443104 -> 2187268167088
	2187268166416 -> 2187268166992
	2187268166416 [label=TBackward0]
	2186996735280 -> 2187268166416
	2184990671152 [label="bert.encoder.layer.10.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184990671152 -> 2186996735280
	2186996735280 [label=AccumulateGrad]
	2187268126416 -> 2187268128144
	2187268126416 [label=UnsafeViewBackward0]
	2187268138272 -> 2187268126416
	2187268138272 [label=CloneBackward0]
	2187268138512 -> 2187268138272
	2187268138512 [label=ExpandBackward0]
	2187268139232 -> 2187268138512
	2187268139232 [label=PermuteBackward0]
	2187268137936 -> 2187268139232
	2187268137936 [label=ViewBackward0]
	2187268154992 -> 2187268137936
	2187268154992 [label=ViewBackward0]
	2187268154128 -> 2187268154992
	2187268154128 [label=AddmmBackward0]
	2186996732160 -> 2187268154128
	2184990658048 [label="bert.encoder.layer.10.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184990658048 -> 2186996732160
	2186996732160 [label=AccumulateGrad]
	2187268153456 -> 2187268154128
	2187268153456 [label=ViewBackward0]
	2187268443104 -> 2187268153456
	2187268166896 -> 2187268154128
	2187268166896 [label=TBackward0]
	2186996472272 -> 2187268166896
	2184990658288 [label="bert.encoder.layer.10.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184990658288 -> 2186996472272
	2186996472272 [label=AccumulateGrad]
	2187268126176 -> 2187268126080
	2187268126176 [label=TBackward0]
	2186996721888 -> 2187268126176
	2184990657808 [label="bert.encoder.layer.10.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184990657808 -> 2186996721888
	2186996721888 [label=AccumulateGrad]
	2187268443104 -> 2187268443728
	2186474633728 -> 2186726614400
	2184990657328 [label="bert.encoder.layer.10.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990657328 -> 2186474633728
	2186474633728 [label=AccumulateGrad]
	2186474633680 -> 2186726614400
	2184990657088 [label="bert.encoder.layer.10.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990657088 -> 2186474633680
	2186474633680 [label=AccumulateGrad]
	2187268441184 -> 2187268441904
	2187268441184 [label=TBackward0]
	2186996719728 -> 2187268441184
	2184990656848 [label="bert.encoder.layer.10.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184990656848 -> 2186996719728
	2186996719728 [label=AccumulateGrad]
	2187268440320 -> 2186996403408
	2187268440320 [label=TBackward0]
	2186996731968 -> 2187268440320
	2184990656368 [label="bert.encoder.layer.10.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184990656368 -> 2186996731968
	2186996731968 [label=AccumulateGrad]
	2186726614400 -> 2186726613776
	2186474632192 -> 2186996391312
	2184990655888 [label="bert.encoder.layer.10.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990655888 -> 2186474632192
	2186474632192 [label=AccumulateGrad]
	2186474632144 -> 2186996391312
	2184990655648 [label="bert.encoder.layer.10.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990655648 -> 2186474632144
	2186474632144 [label=AccumulateGrad]
	2186726615408 -> 2186726615504
	2186726615408 [label=TBackward0]
	2186996732352 -> 2186726615408
	2184990655408 [label="bert.encoder.layer.11.attention.self.query.weight
 (768, 768)" fillcolor=lightblue]
	2184990655408 -> 2186996732352
	2186996732352 [label=AccumulateGrad]
	2186996496128 -> 2186996496560
	2186996496128 [label=UnsafeViewBackward0]
	2186996484560 -> 2186996496128
	2186996484560 [label=CloneBackward0]
	2186726615840 -> 2186996484560
	2186726615840 [label=ExpandBackward0]
	2186726615168 -> 2186726615840
	2186726615168 [label=TransposeBackward0]
	2186726615456 -> 2186726615168
	2186726615456 [label=PermuteBackward0]
	2186726614160 -> 2186726615456
	2186726614160 [label=ViewBackward0]
	2186996403984 -> 2186726614160
	2186996403984 [label=ViewBackward0]
	2187268441472 -> 2186996403984
	2187268441472 [label=AddmmBackward0]
	2186996455696 -> 2187268441472
	2184990654688 [label="bert.encoder.layer.11.attention.self.key.bias
 (768)" fillcolor=lightblue]
	2184990654688 -> 2186996455696
	2186996455696 [label=AccumulateGrad]
	2187268441232 -> 2187268441472
	2187268441232 [label=ViewBackward0]
	2186996391312 -> 2187268441232
	2187268440224 -> 2187268441472
	2187268440224 [label=TBackward0]
	2186996456560 -> 2187268440224
	2184990654928 [label="bert.encoder.layer.11.attention.self.key.weight
 (768, 768)" fillcolor=lightblue]
	2184990654928 -> 2186996456560
	2186996456560 [label=AccumulateGrad]
	2186726627936 -> 2186726628416
	2186726627936 [label=UnsafeViewBackward0]
	2186726626016 -> 2186726627936
	2186726626016 [label=CloneBackward0]
	2186726625536 -> 2186726626016
	2186726625536 [label=ExpandBackward0]
	2186996497040 -> 2186726625536
	2186996497040 [label=PermuteBackward0]
	2186996498096 -> 2186996497040
	2186996498096 [label=ViewBackward0]
	2186726615888 -> 2186996498096
	2186726615888 [label=ViewBackward0]
	2186726613920 -> 2186726615888
	2186726613920 [label=AddmmBackward0]
	2186996457040 -> 2186726613920
	2184990641824 [label="bert.encoder.layer.11.attention.self.value.bias
 (768)" fillcolor=lightblue]
	2184990641824 -> 2186996457040
	2186996457040 [label=AccumulateGrad]
	2186996401536 -> 2186726613920
	2186996401536 [label=ViewBackward0]
	2186996391312 -> 2186996401536
	2186726616128 -> 2186726613920
	2186726616128 [label=TBackward0]
	2186996455264 -> 2186726616128
	2184990642064 [label="bert.encoder.layer.11.attention.self.value.weight
 (768, 768)" fillcolor=lightblue]
	2184990642064 -> 2186996455264
	2186996455264 [label=AccumulateGrad]
	2186996379024 -> 2186726601488
	2186996379024 [label=TBackward0]
	2186996470208 -> 2186996379024
	2184990641584 [label="bert.encoder.layer.11.attention.output.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184990641584 -> 2186996470208
	2186996470208 [label=AccumulateGrad]
	2186996391312 -> 2186726654592
	2186474620048 -> 2186474767264
	2184990641104 [label="bert.encoder.layer.11.attention.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990641104 -> 2186474620048
	2186474620048 [label=AccumulateGrad]
	2186474620000 -> 2186474767264
	2184990640864 [label="bert.encoder.layer.11.attention.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990640864 -> 2186474620000
	2186474620000 [label=AccumulateGrad]
	2186726656800 -> 2186726656416
	2186726656800 [label=TBackward0]
	2186996472128 -> 2186726656800
	2184990640624 [label="bert.encoder.layer.11.intermediate.dense.weight
 (3072, 768)" fillcolor=lightblue]
	2184990640624 -> 2186996472128
	2186996472128 [label=AccumulateGrad]
	2186726657760 -> 2186726657808
	2186726657760 [label=TBackward0]
	2186996472752 -> 2186726657760
	2184990640144 [label="bert.encoder.layer.11.output.dense.weight
 (768, 3072)" fillcolor=lightblue]
	2184990640144 -> 2186996472752
	2186996472752 [label=AccumulateGrad]
	2186474767264 -> 2186474770336
	2186474528336 -> 2186474767744
	2184990639664 [label="bert.encoder.layer.11.output.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990639664 -> 2186474528336
	2186474528336 [label=AccumulateGrad]
	2186474528288 -> 2186474767744
	2184990639424 [label="bert.encoder.layer.11.output.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990639424 -> 2186474528288
	2186474528288 [label=AccumulateGrad]
	2186474768800 -> 2186474768080
	2186474768800 [label=TBackward0]
	2186996456992 -> 2186474768800
	2184990842768 [label="cls.predictions.transform.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184990842768 -> 2186996456992
	2186996456992 [label=AccumulateGrad]
	2186474527664 -> 2186474768128
	2184980272688 [label="cls.predictions.transform.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184980272688 -> 2186474527664
	2186474527664 [label=AccumulateGrad]
	2186474527616 -> 2186474768128
	2184990842448 [label="cls.predictions.transform.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990842448 -> 2186474527616
	2186474527616 [label=AccumulateGrad]
	2186996745360 -> 2186474797232
	2186996745360 [label=TBackward0]
	2186726601200 -> 2186996745360
	2186917551408 -> 2186917551024
	2186917551408 [label=MulBackward0]
	2186726489632 -> 2186917551408
	2186726489632 [label=DivBackward0]
	2186474789808 -> 2186726489632
	2186474789808 [label=SumBackward0]
	2186474789520 -> 2186474789808
	2186474789520 [label=MulBackward0]
	2186726604656 -> 2186474789520
	2186726604656 [label=DivBackward0]
	2186474789568 -> 2186726604656
	2186474789568 [label=AddBackward0]
	2186474768272 -> 2186474789568
	2186474768272 [label=AddBackward0]
	2186474768752 -> 2186474768272
	2186474768752 [label=NllLossBackward0]
	2186474766496 -> 2186474768752
	2186474766496 [label=LogSoftmaxBackward0]
	2186726656752 -> 2186474766496
	2186726656752 [label=ViewBackward0]
	2186726656512 -> 2186726656752
	2186726656512 [label=ViewBackward0]
	2186726655696 -> 2186726656512
	2186726655696 [label=AddmmBackward0]
	2186996441680 -> 2186726655696
	2184990841808 [label="cls.Phonetic_relationship.sm_classifier.bias
 (24)" fillcolor=lightblue]
	2184990841808 -> 2186996441680
	2186996441680 [label=AccumulateGrad]
	2186726656224 -> 2186726655696
	2186726656224 [label=ViewBackward0]
	2186996379264 -> 2186726656224
	2186996379264 [label=NativeLayerNormBackward0]
	2186726628656 -> 2186996379264
	2186726628656 [label=GeluBackward0]
	2186726626784 -> 2186726628656
	2186726626784 [label=ViewBackward0]
	2186726626640 -> 2186726626784
	2186726626640 [label=AddmmBackward0]
	2186996429776 -> 2186726626640
	2184990842128 [label="cls.Phonetic_relationship.transform.dense.bias
 (768)" fillcolor=lightblue]
	2184990842128 -> 2186996429776
	2186996429776 [label=AccumulateGrad]
	2186726626544 -> 2186726626640
	2186726626544 [label=ViewBackward0]
	2186474767744 -> 2186726626544
	2186726629232 -> 2186726626640
	2186726629232 [label=TBackward0]
	2186996429440 -> 2186726629232
	2184990842208 [label="cls.Phonetic_relationship.transform.dense.weight
 (768, 768)" fillcolor=lightblue]
	2184990842208 -> 2186996429440
	2186996429440 [label=AccumulateGrad]
	2186996428912 -> 2186996379264
	2184990842048 [label="cls.Phonetic_relationship.transform.LayerNorm.weight
 (768)" fillcolor=lightblue]
	2184990842048 -> 2186996428912
	2186996428912 [label=AccumulateGrad]
	2186996429968 -> 2186996379264
	2184990841968 [label="cls.Phonetic_relationship.transform.LayerNorm.bias
 (768)" fillcolor=lightblue]
	2184990841968 -> 2186996429968
	2186996429968 [label=AccumulateGrad]
	2186726655888 -> 2186726655696
	2186726655888 [label=TBackward0]
	2186996428960 -> 2186726655888
	2184990841888 [label="cls.Phonetic_relationship.sm_classifier.weight
 (24, 768)" fillcolor=lightblue]
	2184990841888 -> 2186996428960
	2186996428960 [label=AccumulateGrad]
	2186474766976 -> 2186474768272
	2186474766976 [label=NllLossBackward0]
	2186726657424 -> 2186474766976
	2186726657424 [label=LogSoftmaxBackward0]
	2186726657664 -> 2186726657424
	2186726657664 [label=ViewBackward0]
	2186996379216 -> 2186726657664
	2186996379216 [label=ViewBackward0]
	2186726628848 -> 2186996379216
	2186726628848 [label=AddmmBackward0]
	2186996431408 -> 2186726628848
	2184990841648 [label="cls.Phonetic_relationship.ym_classifier.bias
 (35)" fillcolor=lightblue]
	2184990841648 -> 2186996431408
	2186996431408 [label=AccumulateGrad]
	2186996497472 -> 2186726628848
	2186996497472 [label=ViewBackward0]
	2186996379264 -> 2186996497472
	2186996497856 -> 2186726628848
	2186996497856 [label=TBackward0]
	2186996419648 -> 2186996497856
	2184990841728 [label="cls.Phonetic_relationship.ym_classifier.weight
 (35, 768)" fillcolor=lightblue]
	2184990841728 -> 2186996419648
	2186996419648 [label=AccumulateGrad]
	2186474769616 -> 2186474789568
	2186474769616 [label=NllLossBackward0]
	2186474769664 -> 2186474769616
	2186474769664 [label=LogSoftmaxBackward0]
	2186726657712 -> 2186474769664
	2186726657712 [label=ViewBackward0]
	2186726629184 -> 2186726657712
	2186726629184 [label=ViewBackward0]
	2186726616080 -> 2186726629184
	2186726616080 [label=AddmmBackward0]
	2186996419360 -> 2186726616080
	2184990841488 [label="cls.Phonetic_relationship.sd_classifier.bias
 (6)" fillcolor=lightblue]
	2184990841488 -> 2186996419360
	2186996419360 [label=AccumulateGrad]
	2187268440128 -> 2186726616080
	2187268440128 [label=ViewBackward0]
	2186996379264 -> 2187268440128
	2187268441280 -> 2186726616080
	2187268441280 [label=TBackward0]
	2186996419840 -> 2187268441280
	2184990841568 [label="cls.Phonetic_relationship.sd_classifier.weight
 (6, 768)" fillcolor=lightblue]
	2184990841568 -> 2186996419840
	2186996419840 [label=AccumulateGrad]
	2186917551024 -> 2186474662624
}
