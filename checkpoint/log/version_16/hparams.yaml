accelerator: gpu
accumulate_grad_batches: 4
adam_epsilon: 1.0e-08
amp_backend: native
amp_level: null
auto_lr_find: false
auto_scale_batch_size: false
auto_select_gpus: false
batch_size: 8
benchmark: false
bert_path: E:\Code\SCOPE-main\FPT
check_val_every_n_epoch: 1
checkpoint_callback: null
checkpoint_path: null
ckpt_path: null
data_dir: E:\Code\SCOPE-test\dataprocess
default_root_dir: null
detect_anomaly: false
deterministic: false
devices: null
enable_checkpointing: true
enable_model_summary: true
enable_progress_bar: true
fast_dev_run: false
flush_logs_every_n_steps: null
gamma: 1
gpus: 1
gradient_clip_algorithm: null
gradient_clip_val: null
ipus: null
label_file: E:/Code/SCOPE-main/data/test.sighan15.lbl.tsv
limit_predict_batches: 1.0
limit_test_batches: 1.0
limit_train_batches: 1.0
limit_val_batches: 1.0
log_every_n_steps: 50
log_gpu_memory: null
logger: true
lr: 5.0e-05
max_epochs: 1
max_length: 512
max_steps: -1
max_time: null
min_epochs: null
min_steps: null
mode: train
move_metrics_to_cpu: false
multiple_trainloader_mode: max_size_cycle
num_nodes: 1
num_processes: 1
num_sanity_val_steps: 2
overfit_batches: 0.0
plugins: null
precision: 32
prepare_data_per_node: null
process_position: 0
profiler: null
progress_bar_refresh_rate: null
reload_dataloaders_every_epoch: false
reload_dataloaders_every_n_epochs: 0
replace_sampler_ddp: true
resume_from_checkpoint: null
save_path: E:\Code\SCOPE-test\checkpoint
save_topk: 5
stochastic_weight_avg: false
strategy: null
sync_batchnorm: false
terminate_on_nan: null
tpu_cores: null
track_grad_norm: -1
use_memory: false
val_check_interval: 1.0
warmup_proporation: 0.01
warmup_steps: 0
weight_decay: 0.01
weights_save_path: null
weights_summary: top
workers: 8
