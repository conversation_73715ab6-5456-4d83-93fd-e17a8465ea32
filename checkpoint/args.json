{"bert_path": "/home/<USER>/wangjiah<PERSON>/Code/SCOPE-main/FPT", "data_dir": "/home/<USER>/wangjiahao/Code/SCOPE-test/dataprocess", "label_file": "/home/<USER>/wangjiahao/Code/SCOPE-main/data/test.sighan15.lbl.tsv", "save_path": "/home/<USER>/wangjiah<PERSON>/Code/SCOPE-test/checkpoint", "batch_size": 8, "lr": 5e-05, "workers": 8, "weight_decay": 0.01, "adam_epsilon": 1e-08, "warmup_steps": 0, "use_memory": false, "max_length": 512, "checkpoint_path": null, "save_topk": 5, "mode": "train", "warmup_proporation": 0.01, "gamma": 1, "ckpt_path": null, "logger": true, "checkpoint_callback": null, "enable_checkpointing": true, "default_root_dir": null, "gradient_clip_val": null, "gradient_clip_algorithm": null, "process_position": 0, "num_nodes": 1, "num_processes": 1, "devices": null, "gpus": 1, "auto_select_gpus": false, "ipus": null, "log_gpu_memory": null, "progress_bar_refresh_rate": null, "enable_progress_bar": true, "overfit_batches": 0.0, "track_grad_norm": -1, "check_val_every_n_epoch": 1, "fast_dev_run": false, "accumulate_grad_batches": 4, "max_epochs": 10, "min_epochs": null, "max_steps": -1, "min_steps": null, "max_time": null, "limit_train_batches": 1.0, "limit_val_batches": 1.0, "limit_test_batches": 1.0, "limit_predict_batches": 1.0, "val_check_interval": 1.0, "flush_logs_every_n_steps": null, "log_every_n_steps": 50, "accelerator": "gpu", "strategy": null, "sync_batchnorm": false, "precision": 32, "enable_model_summary": true, "weights_summary": "top", "weights_save_path": null, "num_sanity_val_steps": 2, "resume_from_checkpoint": null, "profiler": null, "benchmark": false, "deterministic": false, "reload_dataloaders_every_n_epochs": 0, "reload_dataloaders_every_epoch": false, "auto_lr_find": false, "replace_sampler_ddp": true, "detect_anomaly": false, "auto_scale_batch_size": false, "prepare_data_per_node": null, "plugins": null, "amp_backend": "native", "amp_level": null, "move_metrics_to_cpu": false, "multiple_trainloader_mode": "max_size_cycle", "stochastic_weight_avg": false, "terminate_on_nan": null}